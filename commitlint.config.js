module.exports = {
    extends: ['@commitlint/config-conventional'],
    parserPreset: {
        parserOpts: {
            headerPattern: /^(\w*)(?:\((.*)\))?:\s(.*)$/,
            headerCorrespondence: ['type', 'scope', 'subject'],
        },
    },
    rules: {
        'type-enum': [
            2,
            'always',
            [
                'feat', // 新功能（feature）
                'fix', // 修补bug
                'docs', // 文档（documentation）
                'style', // 格式（不影响代码运行的变动）
                'refactor', // 重构（即不是新增功能，也不是修改bug的代码变动）
                'test', // 增加测试
                'revert', // 回滚
                'chore', // 构建过程或辅助工具的变动
                'perf', // 性能优化
            ],
        ],
        'type-empty': [2, 'never'], // 提交不符合规范时,也可以提交,但是会有警告
        'subject-empty': [2, 'never'], // 提交不符合规范时,也可以提交,但是会有警告
        'subject-full-stop': [0, 'never'],
        'subject-case': [0, 'never'],
    },
};
