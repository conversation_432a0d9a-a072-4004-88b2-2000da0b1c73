const fs = require('fs');
const path = require('path');

const jsBundlePath = path.resolve(__dirname, '../../dist/static/js');

const jsDir = fs.readdirSync(jsBundlePath);
for (let js of jsDir) {
    const jsPath = path.join(jsBundlePath, js);
    let jsFile = fs.readFileSync(jsPath, 'utf-8');
    if (jsFile.includes('____ActivityOnlineTag____flag')) {
        jsFile = jsFile.replace('____ActivityOnlineTag____flag', '____ActivityOfflineTag____flag');
    } else if (jsFile.includes('____ActivityOfflineTag____flag')) {
        jsFile = jsFile.replace('____ActivityOfflineTag____flag', '____ActivityOnlineTag____flag');
    }
    fs.writeFileSync(jsPath, jsFile, 'utf-8');
}
