const { resolve } = require('path');
const { VueLoaderPlugin } = require('vue-loader');

module.exports = {
    entry: {
        entry: ['./src/main.js'],
    },
    output: {
        path: resolve('dist'),
        filename: '[name].[hash:5].js',
        publicPath: '/',
    },
    resolve: {
        extensions: ['.js', '.vue', '.json'],
        alias: {
            '@': resolve('src'),
            '&': resolve('static/img'),
            // '*lottie': resolve('static/lottie'),
        },
    },
    module: {
        rules: [
            {
                test: /\.vue$/,
                use: [
                    {
                        loader: 'vue-loader',
                        options: {
                            preserveWhitespace: false, // 不保留空格
                        },
                    },
                ],
                include: [resolve('src')],
            },
            {
                test: /\.js$/,
                loader: 'esbuild-loader',
                options: {
                    // JavaScript version to compile to
                    target: 'es2015',
                },
                include: [
                    resolve('src'),
                    /node_modules[\\/]framework7/,
                    /node_modules[\\/]framework7-vue/,
                    /node_modules[\\/]template7/,
                    /node_modules[\\/]dom7/,
                    /node_modules[\\/]@tt/,
                    /node_modules[\\/]axios/,
                    /node_modules[\\/]vue/,
                    /node_modules[\\/]@vue/,
                ],
            },
            {
                test: /\.html$/,
                use: [
                    {
                        loader: 'html-loader',
                        options: {
                            // 配置html中图片编译
                            minimize: true,
                        },
                    },
                ],
            },
            {
                test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
                type: 'asset',
                parser: {
                    dataUrlCondition: {
                        // maxSize: 10 * 1024, // 10kb
                        maxSize: 1,
                    },
                },
                generator: {
                    filename: '[path][name].[hash:9][ext][query]',
                },
            },
            {
                test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
                type: 'asset',
                parser: {
                    dataUrlCondition: {
                        maxSize: 1 * 1024, // 10kb
                    },
                },
                generator: {
                    filename: '[path][name].[hash][ext][query]',
                },
            },
            {
                test: /\.(woff2?|woff|eot|ttf|otf)(\?.*)?$/i,
                type: 'asset',
                parser: {
                    dataUrlCondition: {
                        maxSize: 1 * 1024, // 10kb
                    },
                },
                generator: {
                    filename: '[path][name].[hash][ext][query]',
                },
            },
            {
                test: /\.json$/,
                type: 'asset',
                generator: {
                    filename: '[path][name].[hash][ext][query]',
                },
            },
        ],
    },
    plugins: [
        require('unplugin-vue-components/webpack')({
            /* options */
        }),
        new VueLoaderPlugin(),
    ],
    target: 'web',
};
