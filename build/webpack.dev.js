const { resolve, join } = require('path');
const { merge } = require('webpack-merge');
const FriendlyErrorsWebpackPlugin = require('@soda/friendly-errors-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');
const portfinder = require('portfinder');
const ESLintPlugin = require('eslint-webpack-plugin');
const address = require('address');
const notifier = require('node-notifier');
const { transformer, formatter } = require('./utils/resolveLoaderError');
const common = require('./webpack.common');
const package = require('../package.json');
require('./url-config');

process.env.PORT = 8080;

const devWebpackConfig = merge(common, {
    mode: 'development',
    devtool: 'eval-source-map',
    devServer: {
        static: {
            directory: join(process.cwd(), './static'),
            // directory: resolve('dist'),
            watch: true,
            publicPath: '/',
        },
        hot: true,
        compress: true, // 开启Gzip压缩
        port: 8000, // 端口
        open: false, // 是否默认打开浏览器
        host: 'local-ip',
        allowedHosts: 'all',
        host: '0.0.0.0',
        client: {
            overlay: {
                errors: true,
                warnings: false,
                runtimeErrors: false,
            },
            progress: false,
        },
    },
    resolve: {
        fallback: {
            crypto: false,
        },
    },
    plugins: [
        // new webpack.HotModuleReplacementPlugin(),
        // https://github.com/ampedandwired/html-webpack-plugin
        new HtmlWebpackPlugin({
            filename: 'index.html',
            template: resolve('index.html'),
            inject: true,
        }), 
        new ESLintPlugin({
            context: './', // <-- change context path
            emitError: true,
            emitWarning: true,
            failOnError: true,
            failOnWarning: false,
            extensions: ['js', 'vue'],
            overrideConfigFile: './.eslintrc.js',
        }),
    ],
    module: {
        rules: [
            {
                test: /\.css$/,
                use: ['vue-style-loader', 'css-loader', 'postcss-loader'],
            },
            {
                test: /\.less$/,
                use: [
                    'vue-style-loader',
                    'css-loader',
                    'postcss-loader',
                    'less-loader',
                    {
                        loader: 'style-resources-loader',
                        options: {
                            patterns: resolve('src/less/varsbank.less'),
                            injector: 'append',
                        },
                    },
                ],
            },
        ],
    },
    optimization: {
        chunkIds: 'named',
        emitOnErrors: true,
    },
    cache: {
        type: 'filesystem',
    },
});

module.exports = new Promise((resolve, reject) => {
    portfinder.basePort = process.env.PORT || 8000;
    portfinder.getPort((err, port) => {
        if (err) {
            reject(err);
        } else {
            // publish the new Port, necessary for e2e tests
            process.env.PORT = port;
            // add port to devServer config
            devWebpackConfig.devServer.port = port;

            // Add FriendlyErrorsPlugin
            devWebpackConfig.plugins.push(
                new FriendlyErrorsWebpackPlugin({
                    compilationSuccessInfo: {
                        messages: [`开发环境运行在: http://localhost:${port}`, `开发环境运行在: http://${address.ip()}:${port}`],
                    },
                    additionalTransformers: [transformer],
                    additionalFormatters: [formatter],
                    onErrors: (severity, errors) => {
                        if (severity !== 'error') {
                            return;
                        }
                        const error = errors[0];
                        notifier.notify({
                            title: `${package.name}出错啦~`,
                            message: `${severity} :${error.name}`,
                            subtitle: error.file || `http://${address.ip()}:${port}`,
                        });
                    },
                }),
            );

            resolve(devWebpackConfig);
        }
    });
});
