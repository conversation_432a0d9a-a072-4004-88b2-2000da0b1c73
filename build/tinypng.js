const tinify = require('tinify');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

tinify.key = process.argv[2];
const imgCompressionPath = path.resolve(__dirname, '../imgCompression');
if (!fs.existsSync(imgCompressionPath)) {
    fs.mkdirSync(imgCompressionPath);
}
const hasCompress = (onlyValue) => {
    return fs.existsSync(`${imgCompressionPath}/${onlyValue}`);
};
const failureQueue = [];
let successNumber = 0;
let retryNumber = 0;
let noCompress = 0;
const promises = [];

const compressFile = (filePath, cacheImgCompression, retry) => {
    return new Promise((resolve, reject) => {
        try {
            tinify
                .fromFile(filePath)
                .toFile(cacheImgCompression)
                .then(() => {
                    console.info(`成功#压缩完成图片：${filePath}`);
                    successNumber++;
                    console.log(`缓存cacheImgCompression:${cacheImgCompression}`);
                    fs.copyFileSync(cacheImgCompression, filePath);
                    resolve({
                        complete: true,
                    });
                })
                .catch((error) => {
                    !retry &&
                        failureQueue.push({
                            cacheImgCompression,
                            filePath,
                            error,
                        });
                    reject({
                        complete: false,
                        error,
                    });
                });
        } catch (error) {
            !retry &&
                failureQueue.push({
                    cacheImgCompression,
                    filePath,
                    error,
                });
            reject({
                complete: false,
                error,
            });
        }
    });
};
const dontCompress = (file) => {
    return file.includes('no_compress');
};
const compressDir = (dirPath) => {
    const files = fs.readdirSync(path.resolve(__dirname, dirPath));
    for (let i = 0; i < files.length; i++) {
        const fileStats = fs.lstatSync(path.resolve(__dirname, `${dirPath}${files[i]}`));
        if (fileStats.isFile()) {
            const extname = path.extname(files[i]);
            if (['.png', '.jpg', '.jpeg', '.JPG', '.JPEG'].includes(extname)) {
                const filePath = path.resolve(__dirname, `${dirPath}${files[i]}`);
                const data = fs.readFileSync(filePath);
                const hash = crypto.createHash('sha256');
                hash.update(filePath);
                const pathAndFileNameReplace = `${dirPath}${files[i]}`.replace(/\//gi, '@');
                const onlyValue = hash.digest('hex') + pathAndFileNameReplace;
                const cacheImgCompression = `${imgCompressionPath}/${onlyValue}`;
                console.log(`cacheImgCompression:${cacheImgCompression}`);
                if (dontCompress(files[i])) {
                    console.log(`忽略#图片名中包含no_compress：${filePath}`);
                    noCompress++;
                } else if (hasCompress(onlyValue)) {
                    console.log(`成功#从缓存中读取图片：${filePath}`);
                    fs.copyFileSync(cacheImgCompression, filePath);
                    successNumber++;
                } else {
                    const filePath = path.resolve(__dirname, `${dirPath}${files[i]}`);
                    const compressFilePromise = compressFile(filePath, cacheImgCompression);
                    promises.push(compressFilePromise);
                }
            }
        } else if (fileStats.isDirectory()) {
            compressDir(`${dirPath}${files[i]}/`);
        }
    }
};
const retryFailureQueue = async () => {
    const promisesRetry = [];
    if (failureQueue.length > 0) {
        // 重试错误队列
        retryNumber++;
        console.log(`有图片压缩失败，正在进行第${retryNumber}次重试...`);
        for (let i = 0; i < failureQueue.length; i++) {
            if (!failureQueue[i].success) {
                const retryPromise = new Promise((resolve, reject) => {
                    compressFile(failureQueue[i].filePath, failureQueue[i].cacheImgCompression, true)
                        .then(() => {
                            fs.copyFileSync(failureQueue[i].cacheImgCompression, failureQueue[i].filePath);
                            failureQueue[i].success = true;
                            resolve({
                                complete: true,
                            });
                        })
                        .catch((error) => {
                            // console.warn(error)
                            failureQueue[i].retryError = error.error;
                            // console.warn(`${failureQueue[i].filePath}压缩失败`);
                            reject({
                                complete: false,
                            });
                        });
                });
                promisesRetry.push(retryPromise);
            }
        }
    }
    await Promise.allSettled(promisesRetry);
};
const statisticalFailure = () => {
    let failureNumber = 0;
    for (let i = 0; i < failureQueue.length; i++) {
        if (!failureQueue[i].success) {
            failureNumber++;
        }
    }
    if (failureNumber <= 0) {
        console.log(`

    ------------------压缩结束，成功${successNumber}张，忽略${noCompress}张------------------

    `);
    } else {
        console.log(`

    ------------------压缩结束，成功${successNumber}张，忽略${noCompress}张，失败${failureNumber}张，失败信息如下：------------------

    `);
    }

    for (let i = 0; i < failureQueue.length; i++) {
        if (!failureQueue[i].success) {
            console.warn(`失败图片：${failureQueue[i].filePath}`);
            console.warn(`首次失败错误信息：${failureQueue[i].error}`);
            console.warn(`重试失败错误信息：${failureQueue[i].retryError}`);
        }
    }
};
const compress = async () => {
    const imgPath = '../dist/static/img/';
    compressDir(imgPath);
    await Promise.allSettled(promises);
    await retryFailureQueue();
    statisticalFailure();
};
compress();
