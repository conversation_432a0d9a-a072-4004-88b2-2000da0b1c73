const { merge } = require('webpack-merge');
const { resolve, posix } = require('path');
const { ESBuildMinifyPlugin } = require('esbuild-loader');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const webpack = require('webpack');
const common = require('./webpack.common');


const config = merge(common, {
    mode: 'production',

    output: {
        filename: posix.join('static', 'js/[name].[contenthash:5].js'),
        publicPath: '',
        clean: true,
        environment: {
            arrowFunction: false,
        },
    },
    resolve: {
        fallback: {
            crypto: false,
        },
    },
    module: {
        noParse: /jquery|lodash/,
        rules: [
            {
                test: /\.css$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            publicPath: '../',
                        },
                    },
                    'css-loader',
                    'postcss-loader',
                ],
            },
            {
                test: /\.less$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader,
                        options: {
                            publicPath: '../../',
                        },
                    },
                    'css-loader',
                    'postcss-loader',
                    'less-loader',
                    {
                        loader: 'style-resources-loader',
                        options: {
                            patterns: resolve('src/less/varsbank.less'),
                            injector: 'append',
                        },
                    },
                ],
            },
        ],
    },
    plugins: [
        new MiniCssExtractPlugin({
            // 分离css
            filename: posix.join('static', 'css/[name].[contenthash:5].css'),
        }),
        new webpack.ProgressPlugin({
            handler(percentage, message, ...args) {
                console.log(`${(percentage * 100).toFixed(2)}%`, message, ...args);
            },
        }),
        new HtmlWebpackPlugin({
            filename: 'index.html',
            title: 'production',
            template: 'index.html',
            inject: true,
            minify: true,
        }),

    ],
    optimization: {
        minimize: true,
        minimizer: [
            new ESBuildMinifyPlugin({
                target: 'es2015',
                css: true,
            }),
        ],
        chunkIds: 'named',
        splitChunks: {
            chunks: 'all',
            minSize: 20000,
            cacheGroups: {
                vendors: {
                    name: 'chunk-vendors',
                    test: /[\\/]node_modules[\\/]/,
                    priority: 10,
                    // chunks: 'async',
                    // reuseExistingChunk: true,
                },
                commons: {
                    name: 'chunk-common',
                    minChunks: 2,
                    priority: 5,
                    // chunks: 'async',
                    reuseExistingChunk: true,
                },
                framework7: {
                    name: 'chunk-framework7',
                    priority: 15,
                    // chunks: 'async',
                    test: /[\\/]node_modules[\\/]_?framework7(.*)/,
                },
                framework7Vue: {
                    name: 'chunk-framework7Vue',
                    priority: 20,
                    // chunks: 'async',
                    test: /[\\/]node_modules[\\/]_?framework7-vue(.*)/,
                },
                activitylib: {
                    name: 'chunk-activity-lib',
                    priority: 20,
                    // chunks: 'async',
                    test: /[\\/]node_modules[\\/]_?@tt(.*)/,
                },
                tailwindcss: {
                    name: 'chunk-tailwindcss',
                    priority: 20,
                    // chunks: 'async',
                    test: /[\\/]node_modules[\\/]_?tailwindcss(.*)/,
                },
            },
        },
    },
    target: ['web', 'es5'],
});

if (process.env.npm_config_report) {
    const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
    config.plugins.push(new BundleAnalyzerPlugin());
}

module.exports = config;
