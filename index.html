<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, minimal-ui, viewport-fit=cover"
        />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="format-detection" content="telephone=no" />
        <meta name="msapplication-tap-highlight" content="no" />
        <meta name="revised" id="html-version" content="3.0" />
        <meta http-equiv="Pragma" content="no-cache" />
        <meta http-equiv="Cache-Control" content="no-cache" />
        <meta http-equiv="Expires" content="0" />
        <title>anniversary-guild-honor-2025</title>

        <link rel="stylesheet" href="https://ga-album-cdnqn.52tt.com/web/framework7/5.7.12/framework7.min.css" />

        <!-- 构建样式表自动注入 -->
        <!-- wechat sdk -->
        <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js" type="text/javascript"></script>
    </head>
    <body>
        <div id="app"></div>
        <script>
            // CSS预加载 polyfill
            !(function (t) {
                'use strict';
                t.loadCSS || (t.loadCSS = function () {});
                var e = (loadCSS.relpreload = {});
                if (
                    ((e.support = (function () {
                        var e;
                        try {
                            e = t.document.createElement('link').relList.supports('preload');
                        } catch (t) {
                            e = !1;
                        }
                        return function () {
                            return e;
                        };
                    })()),
                    (e.bindMediaToggle = function (t) {
                        var e = t.media || 'all';
                        function a() {
                            t.media = e;
                        }
                        t.addEventListener
                            ? t.addEventListener('load', a)
                            : t.attachEvent && t.attachEvent('onload', a),
                            setTimeout(function () {
                                (t.rel = 'stylesheet'), (t.media = 'only x');
                            }),
                            setTimeout(a, 3e3);
                    }),
                    (e.poly = function () {
                        if (!e.support())
                            for (var a = t.document.getElementsByTagName('link'), n = 0; n < a.length; n++) {
                                var o = a[n];
                                'preload' !== o.rel ||
                                    'style' !== o.getAttribute('as') ||
                                    o.getAttribute('data-loadcss') ||
                                    (o.setAttribute('data-loadcss', !0), e.bindMediaToggle(o));
                            }
                    }),
                    !e.support())
                ) {
                    e.poly();
                    var a = t.setInterval(e.poly, 500);
                    t.addEventListener
                        ? t.addEventListener('load', function () {
                              e.poly(), t.clearInterval(a);
                          })
                        : t.attachEvent &&
                          t.attachEvent('onload', function () {
                              e.poly(), t.clearInterval(a);
                          });
                }
                'undefined' != typeof exports ? (exports.loadCSS = loadCSS) : (t.loadCSS = loadCSS);
            })('undefined' != typeof global ? global : this);
        </script>
        <!-- 构建Javascript自动注入 -->

        <script
            type="text/javascript"
            src="https://obs-cdn.52tt.com/tt/fe-moss/web/lib/fullpage/4.0.14/fullpage.min.js"
        ></script>
    </body>
</html>
