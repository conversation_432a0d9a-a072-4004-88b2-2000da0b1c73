import { currConfig } from '@/config/urlConfig';
import myWebview from './utils/webview-init'; // webview方法
import { getENV } from './config/url';

const osType = myWebview.isIOS() ? 'ios' : 'android';

const env = getENV();

console.log('当前环境', env);

export const WEB_HOST = currConfig[`web_${osType}`]?.[env];
export const WEB_HOST_SHARE = currConfig[`web_${osType}_share`]?.[env];
export const BELUGA_HOST = currConfig[`beluga_${osType}`]?.prod;

export const mainEntry = `${WEB_HOST}sub-venue-3-2507/index.html?immersion=1`; // 聚合页链接
