//全局样式
body {
    max-width: 750px;
}
ul {
    list-style: none;
}
li {
    list-style-type: none;
}

// 1px边框
[class*='mf-hairline'] {
    &::after {
        .hairline();
    }
}
.mf-hairline {
    &,
    &_top,
    &_right,
    &_bottom,
    &_left,
    &_surround,
    &_top-bottom {
        position: relative;
    }

    &_top::after {
        border-top-width: @border-width-base;
    }
    &_right::after {
        border-right-width: @border-width-base;
    }
    &_left::after {
        border-left-width: @border-width-base;
    }
    &_bottom::after {
        border-bottom-width: @border-width-base;
    }

    &,
    &-unset {
        &_top-bottom::after {
            border-width: @border-width-base 0;
        }
    }

    &_surround::after {
        border-width: @border-width-base;
    }
}

.mf-multi-ellipsis--l2 {
    .multi-ellipsis(2);
}
.mf-multi-ellipsis--l3 {
    .multi-ellipsis(3);
}

.fade-enter-active {
    transition: all 0.3s ease;
}
.fade-leave-active {
    transition: all 0.3s ease;
}
.fade-enter, .fade-leave-to
/* .slide-fade-leave-active for below version 2.1.8 */ {
    opacity: 0;
}

.slide-fade-enter-active {
    transition: all 0.5s ease;
}
.slide-fade-leave-active {
    transition: all 0.5s ease;
}
.slide-fade-enter, .slide-fade-leave-to
/* .slide-fade-leave-active for below version 2.1.8 */ {
    transform: translateY(100%);
    opacity: 0;
}

// 麦位框
.head-frame {
    z-index: 1;
    position: absolute;
    top: 50%;
    left: 50%;
    width: 90px;
    height: 93px;
    transform-origin: 0 0;
    background-size: cover;
    background-position: 0 0;
    animation: head-frame 1s steps(6) infinite;
    -webkit-animation: head-frame 1s steps(6) infinite;
}

@head-bg-height: 93px; //31的倍数，否则动图会左右摇晃

@keyframes head-frame {
    0% {
        background-position: 0 0;
    }

    100% {
        background-position: -(@head-bg-height*1080 / 186) 0;
    }
}

/*!
 * fullPage 3.0.9
 * https://github.com/alvarotrigo/fullPage.js
 *
 * @license GPLv3 for open source use only
 * or Fullpage Commercial License for commercial use
 * http://alvarotrigo.com/fullPage/pricing/
 *
 * Copyright (C) 2018 http://alvarotrigo.com/fullPage - A project by Alvaro Trigo
 */
.fp-enabled body,
html.fp-enabled {
    margin: 0;
    padding: 0;
    overflow: hidden;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.fp-section {
    position: relative;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

.fp-slide {
    float: left;
}

.fp-slide,
.fp-slidesContainer {
    height: 100%;
    display: block;
}

.fp-slides {
    z-index: 1;
    height: 100%;
    overflow: hidden;
    position: relative;
    -webkit-transition: all 0.3s ease-out;
    transition: all 0.3s ease-out;
}

.fp-section.fp-table,
.fp-slide.fp-table {
    display: table;
    table-layout: fixed;
    width: 100%;
}

.fp-tableCell {
    display: table-cell;
    vertical-align: middle;
    width: 100%;
    height: 100%;
}

.fp-slidesContainer {
    float: left;
    position: relative;
}

.fp-controlArrow {
    -webkit-user-select: none;
    -moz-user-select: none;
    -khtml-user-select: none;
    -ms-user-select: none;
    position: absolute;
    z-index: 4;
    top: 205px;
    width: 36px;
    height: 36px;
    cursor: pointer;
}

.fp-controlArrow.fp-prev {
    left: 13px;
    background: url('../../static/img/<EMAIL>') no-repeat center;
    background-size: 100% 100%;
}

.fp-controlArrow.fp-next {
    // right: 15px;
    right: 13px;
    background: url('../../static/img/<EMAIL>') no-repeat center;
    background-size: 100% 100%;
    // border-width: 38.5px 0 38.5px 34px;
    // border-color: transparent transparent transparent #fff
}

.fp-auto-height .fp-slide,
.fp-auto-height .fp-tableCell,
.fp-auto-height.fp-section {
    height: auto !important;
}

.fp-responsive .fp-auto-height-responsive .fp-slide,
.fp-responsive .fp-auto-height-responsive .fp-tableCell,
.fp-responsive .fp-auto-height-responsive.fp-section {
    height: auto !important;
}

.section-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-bottom: 80px;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}
.section-bg {
    background-image: url('/static/img/<EMAIL>');
    background-position: center top;
    background-repeat: no-repeat;
    background-size: 100%;
    background-color: #0f0f10;
}

.section-bg-guide {
    background-image: url('/static/img/<EMAIL>');
    background-position: center top;
    background-repeat: no-repeat;
    background-size: 100%;
    background-color: #0f0f10;
}

.section:last-child .arrow-btn-box {
    display: none !important;
}
