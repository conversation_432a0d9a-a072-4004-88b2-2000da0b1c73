<template>
    <f7-app :params="f7params"
            id="app">
        <f7-view main
                 url="/"></f7-view>
        <activity-end-modal />
        <current-server-time v-if="!isProd" />
    </f7-app>
</template>

<script>
import { f7ready } from 'framework7-vue/utils/plugin';
import { toast, preloader, modal } from '@/utils/tools';
import CurrentServerTime from '@/components/common/current-server-time';
import { getENV } from '@/config/url';
import routes from './routes';
import config from './config';
import ActivityEndModal from './components/common/activity-end-modal';

const isProd = getENV() === 'prod';

export default {
    name: 'app',
    components: {
        ActivityEndModal,
        CurrentServerTime,
    },
    data() {
        return {
            f7params: {
                id: 'com.myapp.app',
                name: 'TT EF',
                theme: 'ios',
                routes,
                on: {
                    routeChange: (newRoute) => {
                        // 修改标题
                        window.document.title = newRoute?.route.title;
                        if (window.myWebview.isInApp()) {
                            try {
                                // eslint-disable-next-line no-undef
                                TTJSBridge.invoke(
                                    'ui',
                                    'setCurrentPageTitle',
                                    JSON.stringify({ title: window.document.title }),
                                );
                            } catch (e) {
                                // continue regardless of error
                            }
                        }
                    },
                },
                view: {
                    pushState: true,
                },
            },
            isProd,
        };
    },
    created() {
        this.$eventBus.$on('HTML_LOADED', this.changedHtmlStatus);
        this.$eventBus.$on('F7_READY', this.changedF7Status);

        window.onload = () => this.$eventBus.$emit('HTML_LOADED');
        f7ready((f7) => {
            window.f7toast = toast(f7);
            window.f7preloader = preloader(f7);
            window.f7modal = modal(f7);
            window.f7 = this.$f7; // 挂在到全局解决babel问题
            this.$eventBus.$emit('F7_READY');
            // eslint-disable-next-line
            if (config.__isOffline.replace('flag', '') === '____ActivityOfflineTag____') {
                window.f7.dialog.open(window.f7.$('#activityEndModal'));
            }
        });
    },
    beforeDestroy() {
        this.$eventBus.$off('HTML_LOADED', this.changedHtmlStatus);
        this.$eventBus.$off('F7_READY', this.changedF7Status);
    },
    methods: {
        changedHtmlStatus() {
            this.$store.commit('HTML_LOADED', true);
        },
        changedF7Status() {
            this.$store.commit('F7_READY', true);
        },
    },
};
</script>

<style lang="less">
// @import './less/style.less';

@font-face {
    font-family: 'HYLingXinSquare';
    src: url('/static/font/alimama.ttf');
    font-display: swap;
}

.alimama {
    font-family: 'HYLingXinSquare' !important;
}

#app {
    padding: 0;
    *::-webkit-scrollbar {
        display: none;
    }
    .page-content {
        padding: 0;
    }
}
</style>
