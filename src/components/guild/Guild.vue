<template>
    <div class="section-item section-bg-guide">
        <div class="relative">
            <Rank-title class="mt-[50px]">年度公会榜</Rank-title>
            <Rank-right-tab />
            <div class="guild-tab">
                <div
                    :class="['guild-tab-item', { 'is-live': item.isLive }]"
                    v-for="(item, index) in tabList"
                    :key="`guild-tab-item-${index}`"
                    @click="changeTab(item.value)"
                >
                    <p class="desc-text">{{ item.desc }}</p>
                    <p class="title-text">{{ item.title }}</p>
                </div>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
import config from '@/config';

export default {
    data() {
        return {
            currentTab: 'HonorGuild',
        };
    },
    computed: {
        tabList() {
            const list = [
                {
                    title: '传奇赛区TOP10',
                    desc: '娱乐公会赛',
                    value: 'HonorGuild',
                },
                {
                    title: '星耀赛区TOP10',
                    desc: '娱乐公会赛',
                    value: 'KingGuild',
                },
                {
                    title: '超凡赛区TOP10',
                    desc: '听听公会赛',
                    value: 'LiveKingGuild',
                    isLive: true,
                },
                {
                    title: '荣耀赛区TOP3',
                    desc: '听听公会赛',
                    value: 'DiamondGuild',
                    isLive: true,
                },
            ];
            if (!this.isRevenueBegin) {
                return list.filter((item) => config.visiblePages.indexOf(item.value) !== -1);
            }
            return list;
        },
    },
    methods: {
        changeTab(value) {
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.guild-tab {
    margin-top: 30px;
    min-height: 330px;
}

.guild-tab-item {
    margin: 26px auto;
    padding-top: 21px;
    width: 238px;
    height: 73.5px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    &.is-live {
        background-image: url('/static/img/<EMAIL>');
    }

    >.title-text {
        margin-top: -5px;
        font-size: 17px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg, #451908 0%, #d7611a 100%));
        line-height: 23px;
        text-align: center;
    }

    >.desc-text {
        font-size: 11px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg, #451908 0%, #d7611a 100%); );
        line-height: 17px;
        text-align: center;
    }
}

.guild-tab-item+.guild-tab-item {
    margin-top: 7px;
}
</style>
