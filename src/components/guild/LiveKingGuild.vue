<template>
    <div class="section-item section-bg">
        <Nav-btn />
        <Rank-details-content ref="rankDetailsContent"
                              :is-recreation="false"
                              rank-title="超凡赛区TOP10"
                              rank-type-text="听听公会赛"
                              :rank-list="rankList"
                              id="LiveKingGuild" />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.liveGuildRank.great || [];
        },
    },
};
</script>
