<template>
    <div class="rank-right-tab">
        <div
            :class="['tab-item', { 'is-active': currentTab === item.value }]"
            v-for="(item, index) in tabList"
            :key="`tab-item-${index}`"
            @click="changeTab(item.value)"
        >
            <p
                class="tab-text"
                v-html="item.text"
            ></p>
        </div>
    </div>
</template>

<script>
import config from '@/config';

export default {
    props: {
        currentTab: {
            type: String,
            default: 'Guild',
        },
    },
    computed: {
        tabList() {
            const list = [
                {
                    text: '周年庆<br/>公会榜',
                    value: 'Guild',
                },
                // {
                //     text: '娱乐<br/>强厅赛',
                //     value: 'QiangTing',
                // },
                {
                    text: '娱乐<br/>个人榜',
                    value: 'RecreationPerson',
                },
                {
                    text: '听听<br/>达人榜',
                    value: 'LivePerson',
                },
                {
                    text: '听听<br/>骑士榜',
                    value: 'LiveTalent',
                },
                {
                    text: '听听<br/>才艺榜',
                    value: 'LiveTalent',
                },
                {
                    text: '晶矿<br/>代言人',
                    value: 'OrePerson',
                },
                {
                    text: '娱乐<br/>大人物',
                    value: 'YulePerson',
                },
                // {
                //     text: '黑金大<br/>人物榜',
                //     value: 'RecreationOther', //
                // },
                // {
                //     text: '年度<br/>CP榜',
                //     value: 'RecreationOther',
                // },
            ];
            if (!this.isRevenueBegin) {
                return list.filter((item) => config.visiblePages.indexOf(item.target || item.value) !== -1);
            }
            return list;
        },
    },
    methods: {
        changeTab(value) {
            // console.log('value', value);
            this.toAppointPage(value, 2);
        },
    },
};
</script>

<style lang="less" scoped>
.rank-right-tab {
    position: absolute;
    top: 160px;
    right: 0;
    z-index: 2;
    //height: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .tab-item {
        padding-top: 6px;
        padding-left: 3px;
        width: 46px;
        height: 40px;
        font-size: 11px;
        text-align: center;
        color: #dddddd;
        line-height: 13px;
        .full-bg('../../../static/img/<EMAIL>');

        &.is-active {
            padding-left: 6px;
            width: 53px;
            height: 40px;
            color: #ffe4b4;
            .full-bg('../../../static/img/<EMAIL>');
        }
    }

    .tab-item+.tab-item {
        margin-top: 1px;
    }
}
</style>
