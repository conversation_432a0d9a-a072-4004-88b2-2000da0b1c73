/* eslint-disable */
import { getENV } from '@/config/url';
import { f7ready } from 'framework7-vue/utils/plugin';


const loadComponent = (module) => {
  return async () => {
    // Initializes the share scope. This fills it with known provided modules from this build and all remotes
    // eslint-disable-next-line no-undef
    await __webpack_init_sharing__('default');
    const container = window.hybrid; // or get the container somewhere else
    // Initialize the container, it may provide shared modules
    // eslint-disable-next-line no-undef
    await container.init(__webpack_share_scopes__.default);
    try {
      const factory = await window.hybrid.get(module);
      const Module = factory();
      return Module;
    } catch (error) {
      return undefined;
    }
  };
};
const dynamicScript = (url) => { // 动态加载远程组件
  return new Promise((resolve, reject) => {
    const element = document.createElement('script');
    element.src = url;
    element.type = 'text/javascript';
    element.async = true;

    element.onload = () => {
      // console.log(`动态加载脚本成功: ${url}`);
      // console.log(`动态加载脚本标签移除: ${url}`);
      document.head.removeChild(element);
      resolve();
    };
    element.onerror = () => {
      // console.error(`动态加载脚本失败: ${url}`);
      reject();
    };
    document.head.appendChild(element);
  });
};
let isGlobalInit = false;
const mockTask = (mock) => {
  if (window.__beluga_mockDatas) {
    window.__beluga_mockDatas = Object.assign(window.__beluga_mockDatas, mock);
  } else {
    window.__beluga_mockDatas = mock;
  }
};

export default async (vue, url, vueInstance) => {
  const urlMatching = () => {
    let env = getENV();
    if (url[env]) {
      return `${url[env].replace('index.html', '')}/remote_entry.js?v=${Date.parse(new Date())}`; // 云测试
    } else {
      console.error(`hybrid组件缺少${env}环境的的url配置`);
    }
  };
  await dynamicScript(urlMatching());
  const all = (await loadComponent('all')()).default;
  if (!isGlobalInit) {
    isGlobalInit = true;
    all.global.instantiated(vueInstance, f7ready);
  }
  const components = all.components;
  for (const component of components) {
    const key = Object.keys(component)[0];
    vue.component(key, component[key]);
  }
  const mocks = all.mocks;
  for (const mock of mocks) {
    const key = Object.keys(mock)[0];
    mockTask(mock[key]);
  }
  vue.component('belugaFreeInteractionChildComponents', all.belugaFreeInteractionChildComponents);
  vue.component('hybridRootContainer', all.hybridRootContainer);
  vue.mixin(all.freeInteractionRende);
};
