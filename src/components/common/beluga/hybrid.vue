<template>
  <hybridRootContainer beluga-free-page-name="hybrid"
v-if="isLoadRemote" />
</template>

<script>
import Vue from 'vue';
import loadRemoteComponent from './loadRemoteComponent';

export default {
    props: {
        url: {
            type: Object,
            default() {
                return {};
            },
        },
    },
    data() {
        return {
            isLoadRemote: false,
        };
    },
    async created() {
        await loadRemoteComponent(Vue, this.url, this);
        this.isLoadRemote = true;
    },
    methods: {
        loadMore() {
            this.$eventBus.$emit('page/hybrid/loadMore');
        },
    },
};
</script>
