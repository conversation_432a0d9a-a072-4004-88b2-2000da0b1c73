<template>
    <div class="arrow-btn-box"
         @click="toNextPage">
        <img class="arrow-btn"
             :src="require('/static/img/<EMAIL>')" />
    </div>
</template>

<script>
export default {
    methods: {
        toNextPage() {
            this.$fp.moveSectionDown();
        },
    },
};
</script>

<style lang="less" scoped>
.arrow-btn-box {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    position: absolute;
    left: 50%;
    bottom: 20px;
    margin-left: -100px;
    width: 200px;
    height: 60px;
}
.arrow-btn {
    width: 38px;
    height: 30px;
    // animation: move 1.5s linear infinite;
}
@keyframes move {
    0% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
    50% {
        -webkit-transform: translateY(0);
        transform: translateY(10px);
    }
    100% {
        -webkit-transform: translateY(0);
        transform: translateY(0);
    }
}
// .pulsate-bck {
//     -webkit-animation: pulsate-bck 0.5s ease-in-out infinite both;
//     animation: pulsate-bck 0.5s ease-in-out infinite both;
// }
// /**
//  * ----------------------------------------
//  * animation pulsate-bck
//  * ----------------------------------------
//  */
// @keyframes pulsate-bck {
//     0% {
//         -webkit-transform: scale(1);
//         transform: scale(1);
//     }
//     50% {
//         -webkit-transform: scale(0.9);
//         transform: scale(0.9);
//     }
//     100% {
//         -webkit-transform: scale(1);
//         transform: scale(1);
//     }
// }
</style>
