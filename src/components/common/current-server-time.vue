<template>
    <div class="server-time">当前服务器时间:{{ currentServerTime }}</div>
</template>

<script>
import dayjs from 'dayjs';

export default {
    data() {
        return {};
    },
    computed: {
        currentServerTime() {
            const { serverTime } = this.$store.state || {};
            if (!serverTime) return '暂无服务器时间';
            return dayjs.unix(serverTime).format('YYYY-MM-DD HH:mm:ss');
        },
    },
};
</script>

<style lang="less" scoped>
.server-time {
    position: fixed;
    top: 0;
    left: 0;
    width: 375px;
    line-height: 30px;
    z-index: 99999;
    text-align: center;
    color: white;
    font-size: 16px;
    background-color: rgba(0, 0, 0, 0.3);
    pointer-events: none;
}
</style>
