<template>
    <div :class="['rank-details-content', { 'is-mvp': isMvp }]">
        <div class="relative">
            <!-- <img
                class="absolute left-[34px] top-[-8pxpx] w-[38px] h-[39px]"
                :src="require('/static/img/<EMAIL>')"
            />
            <img
                class="absolute right-[34px] top-[126px] w-[35px] h-[36px]"
                :src="require('/static/img/<EMAIL>')"
            /> -->
            <img
                class="m-center w-[178px] h-[24px]"
                :src="require('/static/img/<EMAIL>')"
            />
            <template v-if="!isMvp">
                <div class="rank-title-h1">{{ rankTypeText }}</div>
                <p :class="['rank-title', { 'is-big': isMvp }, 'title-line']">{{ rankTitle }}</p>
            </template>
            <template v-else>
                <p :class="['rank-title', { 'is-big': isMvp }, 'title-line']">{{ rankTypeText }}</p>
                <p :class="['rank-title', { 'is-big': isMvp }]">{{ rankTitle }}</p>
            </template>

            <!-- <img class="m-center mt-[2px] w-[174px] h-[35px]"
                 :src="require(`/static/img/xqy_title_${isRich?'rys':(isRecreation?(isGuild?1:3):2)}@2x.png`)" /> -->
        </div>
        <!-- <img class="m-center mt-[-25px] w-[374px] h-[50px] mb-[-20px]"
             :src="require('/static/img/<EMAIL>')" /> -->
        <div
            class="relative z-[1] mt-[20px]"
            v-if="rankList && rankList.length"
        >
            <!-- 不用f7-swiper是因为左右滑动时，有时会切换section -->
            <div
                v-for="(item, index) in rankList"
                :key="`swiper-slide-${index}`"
                class="slide"
            >

                <!-- 非 mvp -->
                <div
                    class="ranking"
                    v-if="!isMvp"
                >
                    <p class="title-text">
                        {{ rankingText[item.ranking || item.rank] }}
                    </p>
                </div>

                <!-- 头像昵称部分 -->
                <div class="guild-box">
                    <div class="relative">
                        <img
                            class="guild-head"
                            @click="handleAvatar(getHeadId(item), isGuild ? 3 : 1)"
                            :src="getHeadId(item) | head(isGuild ? 3 : 1)"
                        />
                        <!-- <template v-if="!isGuild">
                            <Room-status v-if="isRecreation"
                                         :item="item?.channelInfo" />
                            <Live-room-status v-else
                                              :item="item?.channelInfo" />
                        </template> -->
                    </div>
                    <p class="guild-name">{{ getName(item) }}</p>
                    <p class="guild-id">{{ isGuild ? '公会' : '' }}ID:{{ getAlias(item) }}</p>
                </div>
            </div>
        </div>

        <!-- 非 mvp 的榜单滚动条 -->
        <div
            class="relative z-[1] mt-[2vh] h-[62px]"
            v-if="!isMvp"
        >
            <div :class="['rank-list-box', { 'is-hide': !isOpen }]">
                <img
                    class="cove-img left-[-1px]"
                    :src="require('/static/img/<EMAIL>')"
                />
                <div
                    class="rank-list w-full"
                    ref="rankList"
                >
                    <img
                        v-for="(item, index) in rankList"
                        @click="changeActiveIndex(index)"
                        ref="head"
                        :key="`guild-head-${index}`"
                        :class="['guild-head', { 'is-active': activeIndex === index }]"
                        class="shrink-0"
                        :src="getHeadId(item) | head(isGuild ? 3 : 1)"
                    />
                </div>
                <img
                    class="cove-img right-[-1px]"
                    :src="require('/static/img/<EMAIL>')"
                />
            </div>
            <div class="operate-btn">
                <img
                    v-show="isOpen"
                    @click="closeRankList"
                    :src="require('/static/img/<EMAIL>')"
                />
                <img
                    v-show="!isOpen"
                    @click="openRankList"
                    :src="require('/static/img/<EMAIL>')"
                />
            </div>
        </div>

    </div>
</template>

<script>
import config from '../../config';
import { scrollIntoViewMiddle } from '../../utils/utils';

export default {
    props: {
        id: {
            type: String,
            default: '',
        },
        isRecreation: {
            type: Boolean,
            default: true,
        },
        isGuild: {
            type: Boolean,
            default: true,
        },
        isRich: {
            type: Boolean,
            default: false,
        },
        isMvp: {
            type: Boolean,
            default: false,
        },
        isChannel: { // 是否是房间
            type: Boolean,
            default: false,
        },
        rankTitle: {
            type: String,
            default: '',
        },
        rankList: {
            type: Array,
            default() {
                return [];
            },
        },
        rankTypeText: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            rankingText: Object.freeze(config.rankingText),
            activeIndex: 0,
            isOpen: true,
            arrowOldDisplay: {
                prev: '',
                next: '',
            },
        };
    },
    watch: {
        activeIndex(newVal) {
            this.$nextTick(() => {
                if (this.$refs.rankList && this.$refs.head[newVal]) {
                    scrollIntoViewMiddle(this.$refs.head[newVal], this.$refs.rankList);
                }
            });
        },
    },
    methods: {
        // 获取头衔id
        getHeadId(info) {
            if (this.isGuild) {
                // console.log(info);
                // 公会
                return info?.guildInfo?.id || info?.guildInfo?.guildId;
            }
            // 个人
            return info?.userInfo?.username || info?.userInfo?.account;
        },
        // 获取展示的id
        getAlias(info) {
            if (this.isGuild) {
                // 公会
                return info?.guildInfo?.displayId;
            }
            // 个人
            return info?.userInfo?.alias;
        },
        getName(info) {
            if (this.isGuild) {
                // 公会
                return info?.guildInfo?.name;
            }
            // 个人
            return info?.userInfo?.nickname;
        },
        changeActiveIndex(value) {
            this.activeIndex = value;
            this.$fp.moveTo(this.id, value);
        },
        changeSlideStatus(value) {
            this.activeIndex = value;
            if (!this.isOpen) {
                if (value === 0) {
                    this.arrowOldDisplay.prev = 'none';
                    this.arrowOldDisplay.next = 'block';
                } else if (value === this.rankList.length - 1) {
                    this.arrowOldDisplay.prev = 'block';
                    this.arrowOldDisplay.next = 'none';
                } else {
                    this.arrowOldDisplay.prev = 'block';
                    this.arrowOldDisplay.next = 'block';
                }
            }
        },
        closeRankList() {
            this.isOpen = false;
            // 暂存当前左右切换箭头的状态
            const arrowPrev = document.querySelector('.section.active .fp-arrow.fp-prev');
            this.arrowOldDisplay.prev = arrowPrev.style.display;
            const arrowNext = document.querySelector('.section.active .fp-arrow.fp-next');
            this.arrowOldDisplay.next = arrowNext.style.display;
            arrowPrev.style.display = 'none';
            arrowNext.style.display = 'none';
        },
        openRankList() {
            this.isOpen = true;
            const arrowPrev = document.querySelector('.section.active .fp-arrow.fp-prev');
            const arrowNext = document.querySelector('.section.active .fp-arrow.fp-next');
            arrowPrev.style.display = this.arrowOldDisplay.prev;
            arrowNext.style.display = this.arrowOldDisplay.next;
        },
    },
};
</script>

<style lang="less" scoped>
.rank-details-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 50px;
    height: 100vh;
}

.rank-title-h1 {
    font-size: 15px;
    font-family: PingFang SC, PingFang SC-Medium;
    font-weight: 500;
    text-align: center;
    color: #dddddd;
    line-height: 16px;
    margin-top: 14px;
}

.rank-title {
    margin-top: 10px;
    font-size: 30px;
    line-height: 36px;
    font-family: 'HYLingXinSquare';
    .text-gradient(linear-gradient(0deg, #f9bc17 0%, #fff2c3 100%));
    text-align: center;
    font-weight: 500;

    &.is-big {
        font-size: 40px;
        line-height: 46px;
    }
}

.title-line {
    position: relative;

    &::after {
        content: '';
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        width: 373.5px;
        height: 50px;
        background-image: url('/static/img/<EMAIL>');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
    }
}

.ranking {
    display: flex;
    align-items: center;
    justify-content: center;

    .arrow-img {
        width: 16px;
        height: 16px;
    }

    .title-text {
        font-size: 45px;
        line-height: 51px;
        font-family: 'HYLingXinSquare';
        text-align: center;
        .text-gradient(linear-gradient(0deg, #f9bc17 0%, #fff2c3 100%));
    }
}

.guild-box {
    margin: 1.5vh auto 0;
    padding-top: 50px;
    width: 295.5px;
    height: 313px;
    .full-bg('../../../static/img/<EMAIL>');

    .guild-head {
        margin-left: 90px;
        .user-head(115px);
    }

    .guild-name {
        margin: 40px auto 0;
        width: 146px;
        font-size: 19px;
        font-weight: bold;
        line-height: 25px;
        text-align: center;
        color: #ffd768;
        // .text-gradient(linear-gradient(0deg,#f9bc17 0%, #fff2c3 100%));
        .one-line();
    }

    .guild-id {
        margin-top: 2px;
        font-size: 13px;
        line-height: 19px;
        text-align: center;
        .text-gradient(linear-gradient(0deg, #f9bc17 0%, #fff2c3 100%));
    }
}

// .button-next,
// .button-prev {
//     top: 205px;
//     margin-top: 0;
//     width: 28px;
//     height: 28px;
// }
// .button-prev {
//     left: 21px;
// }
// .button-next {
//     right: 21px;
// }
.rank-list-box {
    display: flex;
    align-items: center;
    position: relative;
    margin: 0 auto;
    width: 304px;
    height: 61px;
    box-sizing: border-box;
    .full-bg('/static/img/<EMAIL>');

    &.is-hide {
        visibility: hidden;
    }
}

.cove-img {
    position: absolute;
    top: -1px;
    width: 34px;
    height: 62px;
}

.rank-list {
    display: flex;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    box-sizing: border-box;

    &::after {
        content: '';
        padding-right: 34px;
    }

    &::before {
        content: '';
        padding-left: 34px;
    }

    .guild-head {
        .user-head(37px);

        &.is-active {
            border: 2px solid #efd053;
        }
    }

    .guild-head+.guild-head {
        margin-left: 19px;
    }

    .guild-head:first-child {
        margin-left: auto;
    }

    .guild-head:last-child {
        margin-right: auto;
    }
}

.operate-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 17px;
    height: 49px;
}
</style>
