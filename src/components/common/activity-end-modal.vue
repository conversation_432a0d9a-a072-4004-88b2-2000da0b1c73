<template>
  <div class="dialog"
       id="activityEndModal">
    <p class="text">活动已下线</p>
    <p class="tips">去活动中心看看其他活动吧~</p>
  </div>
</template>

<script>
export default {
    name: 'activity-end-modal',
    mounted() {
        this.$nextTick(() => {
            this.$f7.dialog.create({
                el: this.$f7.$('#activityEndModal'),
            });
        });
    },
};
</script>

<style lang="less" scoped>
#activityEndModal {
    margin-left: -135px;
    padding: 45px 0;
    width: 270px;
    background-color: #fff;
    border-radius: 10px;
    font-family: PingFangSC-Medium;
    .text {
        font-size: 16px;
        color: #62a6ff;
    }
    .tips {
        margin-top: 8px;
        font-size: 13px;
        color: #bbb;
    }
}
</style>
