<template>
    <div>
        <!-- StatusBar -->
        <status-bar />

        <!-- Navbar -->
        <page-nav-bar :page-title="pageTitle"
                      :bg-color="bgColor"
                      :color="color"
                      :right-inner="rightInner"
                      :back-fn="backFn"
                      :right-fn="rightFn" />
    </div>
</template>

<script>
import statusBar from './status-bar';
import pageNavBar from './page-nav-bar';

export default {
    components: { statusBar, pageNavBar },
    props: {
        pageTitle: {
            type: String,
            default: '',
        },
        bgColor: {
            type: String,
            default: '',
        },
        color: {
            type: String,
            default: '',
        },
        rightInner: {
            type: String,
            default: '',
        },
        backFn: {
            type: [Function, null],
            default: null,
        },
        rightFn: {
            type: Function,
            default: () => {},
        },
    },
};
</script>
