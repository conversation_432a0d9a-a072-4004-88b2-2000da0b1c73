<template>
    <div id="navbar"
         :style="navStyle">
        <div class="navbar-btn navbar-left"
             @click="onBack">
            <img :src="require('/static/img/<EMAIL>')"
                 alt="icon"
                 class="icon" />
        </div>
        <div class="navbar-title mf-ellipsis"
             v-if="vest === 'tt'">
            <img :src="require(`/static/img/<EMAIL>`)"
                 class="w-[64px] object-contain">
        </div>
        <div class="navbar-btn navbar-right active:scale-[1.05]"
             v-if="isShowRightButton"
             @click="onClickRight"
             v-html="rightInner"></div>
    </div>
</template>

<script>
import { finishActivity, onBackPressed, parseUrlQuery } from '@/utils/webview-init';

export default {
    name: 'page-nav-bar',
    props: {
        bgColor: {
            type: String,
            default: '#ffffff',
        },
        color: {
            type: String,
            default: '#323233',
        },
        backFn: {
            type: [Function, null],
            default: null,
        },
        rightInner: {
            type: String,
            default: '',
        },
        rightFn: {
            type: Function,
            default: () => {},
        },
        pageTitle: {
            type: String,
            default: '',
        },
    },
    data() {
        return {};
    },
    computed: {
        isIOS() {
            const osType = parseUrlQuery().os_type;
            if (osType !== undefined) {
                return parseInt(osType, 10) === 2;
            }
            return this.$f7.device.ios === true;
        },
        navStyle() {
            return `background-color:${this.bgColor};height:${this.isIOS ? 44 : 48}px;color:${this.color};`;
        },
        title() {
            return this.pageTitle || this.routeName;
        },
        isShowRightButton() {
            const { isDev } = parseUrlQuery();
            return this.isInApp || isDev;
        },
        routeName() {
            return this.$store.state.routeName;
        },
        vest() {
            return window.KEY;
        },
    },
    watch: {
        color: {
            immediate: true,
            handler(val) {
                this.changeSvgColor(val);
            },
        },
        rightInner: {
            immediate: true,
            handler(val) {
                this.changeSvgColor(this.color);
            },
        },
    },
    methods: {
        onBack() {
            // 退出按钮拦截
            if (this.backFn && typeof this.backFn === 'function') {
                this.backFn();
                return;
            }
            // 退出webview
            if (this.routeName === 'Home') finishActivity();
            else onBackPressed();
        },
        onClickRight() {
            if (this.rightFn && typeof this.rightFn === 'function') {
                this.rightFn();
            }
        },
        changeSvgColor(val) {
            this.$nextTick(() => {
                const pathList = document.querySelectorAll('.navbar-btn svg path');
                pathList.forEach((item) => {
                    // eslint-disable-next-line no-param-reassign
                    item.style.fill = val;
                });
            });
        },
        onClose() {
            finishActivity();
        },
    },
};
</script>

<style lang="less">
#navbar {
    width: 100vw;
    position: relative;
    line-height: 1.5;
    text-align: center;
    display: flex;
    align-items: center;
    font-size: 14px;
    z-index: 1;
    .navbar {
        &-btn {
            position: absolute;
            top: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: 15px;
            .icon {
                width: 24px;
                height: 24px;
            }
        }
        &-title {
            max-width: 60%;
            margin: 0 auto;
            font-weight: 500;
            font-size: 16px;
        }
        &-left {
            left: 0;
        }
        &-right {
            right: 0;
            // width: 16px;
            /deep/img {
                width: 24px;
                height: 24px;
            }
        }
    }
}
</style>
