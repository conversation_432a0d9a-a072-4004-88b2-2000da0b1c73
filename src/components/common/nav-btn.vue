<template>
    <img class="nav-btn"
         ref="navBtn"
         @click="toHomePage"
         :src="require('/static/img/<EMAIL>')" />
</template>

<script>
export default {
    props: {
        type: {
            type: String,
            default: 'Guild',
        },
    },
    computed: {
        topBarHeight() {
            return this.$store.state.topBarHeight;
        },
    },
    mounted() {
        this.$watch('topBarHeight', (val) => {
            if (val) {
                const { navBtn } = this.$refs;
                if (navBtn) {
                    navBtn.style.top = `${val + 10}px`;
                }
            }
        });
    },
    methods: {
        toHomePage() {
            this.toAppointPage(this.type, 1);
        },
    },
};
</script>

<style lang="less" scoped>
.nav-btn {
    position: absolute;
    z-index: 2;
    top: 10px;
    right: 11px;
    width: 29px;
    height: 29px;
}
</style>
