<template>
    <img class="room-status"
         @click="handleAvatar(item.channelId, 2)"
         :src="require('/static/img/icon_fjz.png')"
         v-if="item.channelId && item.status" />
</template>

<script>
export default {
    props: {
        item: {
            type: Object,
            default() {
                return {};
            },
        },
    },
};
</script>

<style lang="less" scoped>
.room-status {
    position: absolute;
    left: 50%;
    bottom: -2px;
    z-index: 2;
    margin-left: -21px;
    width: 42px;
    height: 16px;
}
</style>
