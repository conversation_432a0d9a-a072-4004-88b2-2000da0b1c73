<template>
    <div class="rank-title">
        <template v-if="showIcon">
            <!-- <img class="absolute left-[34px] top-[-8px] w-[38px] h-[39px]"
                 :src="require('/static/img/<EMAIL>')" />
            <img class="absolute right-[34px] top-[87px] w-[35px] h-[36px]"
                 :src="require('/static/img/<EMAIL>')" /> -->
            <img class="m-center w-[178px] h-[24px]"
                 :src="require('/static/img/<EMAIL>')" />
        </template>
        <p class="title-text">
            <slot></slot>
        </p>
        <img class="m-center mt-[-27px] w-[374px] h-[50px]"
             :src="require('/static/img/<EMAIL>')" />
    </div>
</template>

<script>
export default {
    props: {
        showIcon: {
            type: Boolean,
            default: true,
        },
    },
};
</script>

<style lang="less" scoped>
.rank-title {
    position: relative;
    // display: flex;
    // align-items: center;
    // justify-content: center;
    .arrow-img {
        width: 16px;
        height: 16px;
    }
    .title-text {
        position: relative;
        margin: 0 23px;
        font-size: 45px;
        line-height: 51px;
        font-family: 'HYLingXinSquare';
        text-align: center;
        .text-gradient(linear-gradient(0deg,#f9bc17 0%, #fff2c3 100%));
    }
}
</style>
