<template>
    <div id="status-bar"
         :style="barStyle"></div>
</template>

<script>
import { parseUrlQuery } from '@/utils/webview-init';

export default {
    name: 'status-bar',
    data() {
        return {
            barHeight: 0,
        };
    },
    computed: {
        barStyle() {
            return `height:${this.barHeight}px`;
        },
        isIOS() {
            const osType = window.myWebview.params.urlParams.os_type;
            if (osType !== undefined) {
                return parseInt(osType, 10) === 2;
            }
            return this.$f7.device.ios === true;
        },
    },
    created() {
        const urlParams = parseUrlQuery();
        const { status_height: sh } = urlParams;
        const dpr = window.devicePixelRatio;
        let statusHeight = 0;
        if (sh) {
            statusHeight = sh;
        } else {
            try {
                statusHeight = TTJSBridge.invoke('ui', 'getStatusBar');
            } catch (error) {
                statusHeight = 0;
            }
        }
        if (this.isIOS) {
            this.barHeight = statusHeight;
        } else {
            this.barHeight = Math.floor(statusHeight / dpr);
        }
    },
};
</script>

<style lang="less">
#status-bar {
    width: 100vw;
    position: relative;
    background: transparent;
}
</style>
