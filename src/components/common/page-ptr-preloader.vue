<template>
    <div id="page-ptr-preloader"
class="ptr-preloader">
        <div class="ptr-ani">
            <div class="ptr-ani-circle"
:class="{ 'ani-bound': refreshing }"
:style="circleStyle">
                <svg xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 32 32">
                    <g fill="none"
fill-rule="evenodd">
                        <circle cx="16"
cy="16"
r="14"
stroke="#e0e3e8"
stroke-width="4" />
                    </g>
                </svg>
            </div>
            <div class="ptr-ani-ripple"
:class="{ 'ani-ripple': refreshing }"
ref="ripple">
                <svg xmlns="http://www.w3.org/2000/svg"
viewBox="0 0 48 48">
                    <g fill="none"
fill-rule="evenodd">
                        <circle cx="24"
cy="24"
r="23.6"
stroke="#e0e3e8"
stroke-width=".8" />
                    </g>
                </svg>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'page-ptr-preloader',
    props: {
        pullmove: {
            default: 0,
        },
        ptrDistance: {
            default: 44,
        },
        refreshing: {
            default: false,
        },
    },
    data() {
        return {
            pullEnough: false,
        };
    },
    computed: {
        pullPercent() {
            return this.pullmove === 0 || this.pullmove / this.ptrDistance > 1 ? 1 : this.pullmove / this.ptrDistance;
        },
        circleStyle() {
            return `transform: scale(${this.pullPercent}) translate(-50%, -50%);opacity: ${this.pullPercent};`;
        },
    },
    watch: {
        pullmove(val) {
            this.pullEnough = val > this.ptrDistance;
        },
        pullEnough(newVal, oldVal) {
            if (!oldVal && newVal) {
                this.$refs.ripple.classList.add('ani-ripple');
                setTimeout(() => {
                    this.$refs.ripple.classList.remove('ani-ripple');
                }, 400);
            }
        },
    },
};
</script>

<style lang="less">
#page-ptr-preloader {
    height: 89px;
    position: relative;
    z-index: 1;
    .ptr-ani {
        position: absolute;
        bottom: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 48px;
        height: 48px;
        &-circle {
            width: 24px;
            height: 24px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform-origin: 0 0;
        }
        &-ripple {
            width: 24px;
            height: 24px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: scale(1) translate(-50%, -50%);
            opacity: 0;
            transform-origin: 0 0;
        }
    }
    .ani-ripple {
        opacity: 1;
        animation: 0.4s infinite ripple;
    }
    .ani-bound {
        animation: 0.4s infinite bound;
    }
}

@keyframes ripple {
    from {
        transform: scale(1) translate(-50%, -50%);
        opacity: 1;
    }
    to {
        transform: scale(2) translate(-50%, -50%);
        opacity: 0.4;
    }
}

@keyframes bound {
    0% {
        transform: scale(1) translate(-50%, -50%);
    }
    14.28% {
        transform: scale(0.67) translate(-50%, -50%);
    }
    28.57% {
        transform: scale(1) translate(-50%, -50%);
    }
    42.85% {
        transform: scale(1.34) translate(-50%, -50%);
    }
    57.14% {
        transform: scale(1) translate(-50%, -50%);
    }
    100% {
        transform: scale(1) translate(-50%, -50%);
    }
}
</style>
