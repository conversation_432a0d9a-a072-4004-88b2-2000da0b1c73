<template>
    <f7-page class="full-page">
        <page-top-bar v-if="navFixed"
                      class="fixed-bar"
                      :class="{ 'ptr-transitioning': !isPulling }"
                      :style="`transform: translate3d(0, ${fixedBarTop}px, 0);`"
                      :bg-color="navBgColor"
                      :color="navColor"
                      :page-title="pageTitle"
                      :right-inner="navRightInner"
                      :back-fn="navBackFn"
                      :right-fn="navRightFn" />
        <f7-page-content ptr
                         :ptr-distance="ptrDistance"
                         :ptr-preloader="false"
                         @ptr:refresh="onRefresh"
                         @ptr:pullmove="onPullMove"
                         @ptr:pullend="onPullEnd"
                         @ptr:done="onPullDone"
                         :infinite="infinite"
                         @infinite="onInfinite"
                         :infinite-preloader="infinitePreloader"
                         @scroll.native="onScroll">
            <page-ptr-preloader :pullmove="pullmove"
                                :ptr-distance="ptrDistance"
                                :refreshing="refreshing" />
            <page-top-bar v-if="!navFixed"
                          :bg-color="navBgColor"
                          :color="navColor"
                          :page-title="pageTitle"
                          :right-inner="navRightInner"
                          :back-fn="navBackFn"
                          :right-fn="navRightFn" />
            <img class="page-bg"
                 :src="pageBgImg" />
            <div class="full-page-content">
                <slot></slot>
            </div>
        </f7-page-content>
    </f7-page>
</template>

<script>
/* eslint-disable camelcase */
import pageTopBar from './page-top-bar';
import pagePtrPreloader from './page-ptr-preloader';

export default {
    name: 'full-page',
    components: { pageTopBar, pagePtrPreloader },
    props: {
        pageTitle: {
            default: 'title',
            type: String,
        },
        pageBgImg: {
            default: '',
            type: String,
        },
        navBgColor: {
            default: 'transparent',
            type: String,
        },
        navColor: {
            default: '#fff',
            type: String,
        },
        navRightInner: String,
        navBackFn: Function,
        navRightFn: Function,
        navFixed: {
            default: false,
            type: Boolean,
        },
        infinite: {
            default: false,
            type: Boolean,
        },
        infinitePreloader: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            isPulling: false,
            ptrDistance: 89,
            pullmove: 0,
            refreshing: false,
            fixedBarTop: 0,
        };
    },
    mounted() {
        window.addEventListener('touchstart', () => {
            this.isPulling = true;
        });
        window.addEventListener('touchend', () => {
            this.fixedBarTop =
                (this.pullmove >= this.ptrDistance && this.refreshing) || this.refreshing ? this.ptrDistance : 0;
            this.isPulling = false;
        });
    },
    methods: {
        onRefresh() {
            this.refreshing = true;
            this.$emit('refresh', () => {
                this.$f7.ptr.get('.ptr-refreshing').done();
                this.refreshing = false;
                this.fixedBarTop = 0;
            });
        },
        onPullMove(event) {
            this.pullmove = event.detail.translate;
            this.fixedBarTop = event.detail.translate;
        },
        onInfinite(event) {
            this.$emit('infinite', event);
        },
        onScroll(event) {
            this.$emit('scroll', event);
        },
        onPullEnd(event) {
            this.fixedBarTop = this.pullmove >= this.ptrDistance ? this.ptrDistance : 0;
            this.isPulling = false;
        },
        onPullDone(event) {
            this.pullmove = 0;
        },
    },
};
</script>

<style lang="less">
.full-page {
    text-align: center;
    .ptr-no-navbar {
        margin-top: -89px;
        height: calc(100% + 89px);
    }
    .ptr-refreshing {
        transform: translate3d(0, 89px, 0);
    }
    .page-bg {
        width: 100%;
        position: absolute;
        top: 89px;
        left: 0;
        z-index: 0;
    }
    .fixed-bar {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 9999;
    }
    .full-page-content {
        position: relative;
        z-index: 1;
    }
}
</style>
