<template>
    <img class="room-status"
         @click="handleAvatar(item.channelId, 2)"
         :src="`${require(`/static/img/${roomStatus[status]}.png`)}`"
         v-if="item.channelId && status && roomStatus[status]"
         alt />
</template>

<script>
export default {
    props: {
        item: {
            type: Object,
            default() {
                return {};
            },
        },
    },
    computed: {
        roomStatus() {
            return {
                1: 'icon_fjz',
                2: 'ico_kzb',
                3: 'ico_fjz',
                4: 'ico_pkz',
            };
        },
        status() {
            return this.item.roomStatus || this.item.status || 0;
        },
    },
};
</script>

<style lang="less" scoped>
.room-status {
    position: absolute;
    left: 50%;
    bottom: -2px;
    z-index: 2;
    margin-left: -21px;
    width: 41.5px;
    height: 16px;
}
</style>
