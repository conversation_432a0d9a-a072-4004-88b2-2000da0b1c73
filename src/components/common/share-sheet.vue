<template>
    <div
        class="sheet-modal !w-[375px] !h-[121px] !bg-transparent !backdrop-filter-none before:!hidden"
        id="share-sheet"
    >
        <div
            class="relative w-[100%] h-[100%] bg-default box-border pt-[22px]"
            :style="`backgroundImage: url(${require('/static/img/modal/<EMAIL>')})`"
        >
            <p class="text-center text-[12px] text-[#fff] mb-[15px]">分享</p>
            <div class="flex-center px-[10px] box-border">
                <div
                    class="flex-center-c mr-[10px] last:mr-0 flex-1"
                    v-for="(target, index) in showList"
                    @click="handleClick(target, index)"
                    :key="index"
                >
                    <img
                        :src="target.icon"
                        class="w-[36px] object-contain flex-shrink-0 mb-[2px]"
                    />
                    <p class="text-center text-[#fff] text-[10px]">{{ target.txt }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { getUrlParams } from '@/utils/utils';
import { checkInstallApp } from '@/utils/webview-init';
import { getENV } from '@/config/url';

export default {
    name: 'share-sheet',
    data() {
        return {
            icon_hy: require('/static/img/modal/<EMAIL>'),
            icon_mk: require('/static/img/modal/<EMAIL>'),
            icon_mj: require('/static/img/modal/<EMAIL>'),
            allList: [
                {
                    type: 'WechatMoments',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: '微信朋友圈',
                },
                {
                    type: 'Wechat',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: '微信好友',
                },
                {
                    type: 'TT',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: 'TT好友',
                },
                {
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: '本地保存',
                },
                {
                    type: 'QQ',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: 'QQ好友',
                },
                {
                    type: 'QZone',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: 'QQ空间',
                },
            ],
            normalList: [
                {
                    type: 'WechatMoments',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: '微信朋友圈',
                },
                {
                    type: 'Wechat',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: '微信好友',
                },
                {
                    type: 'TT',
                    icon: require('/static/img/modal/<EMAIL>'),

                    txt: 'TT好友',
                },
                {
                    type: 'QQ',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: 'QQ好友',
                },
                {
                    type: 'QZone',
                    icon: require('/static/img/modal/<EMAIL>'),
                    txt: 'QQ空间',
                },
            ],
            showLocal: false,
            awardBeginTime: 1703498400, // 2023/12/25 18:00:00
        };
    },
    computed: {
        serverTime() {
            return this.$store.state.serverTime;
        },
        routeName() {
            return this.$store.state.routeName;
        },
        showList() {
            let list = this.normalList;
            if (this.routeName === 'Invitation') list = this.allList;
            const key = window.KEY;
            switch (key) {
                case 'hy':
                    list[2].icon = this.icon_hy;
                    list[2].txt = '欢游好友';
                    break;
                case 'mk':
                    list[2].icon = this.icon_mk;
                    list[2].txt = '麦可好友';
                    break;
                case 'mj':
                    list[2].icon = this.icon_mj;
                    list[2].txt = '谜境好友';
                    break;
                default:
                    break;
            }
            return list;
        },
    },
    async mounted() {
        try {
            await this.$nextTick();
            this.$f7.sheet
                .create({
                    el: this.$f7.$('#share-sheet'),
                    closeByBackdropClick: true,
                    backdrop: true,
                })
                .on('setData', this.setData);
        } catch (e) {
            // eslint-disable-next-line
            console.log(e);
        }
        this.$f7.sheet.get(this.$f7.$('#share-sheet')).on('closed', (e) => { });
    },
    methods: {
        closeSheet() {
            this.$f7.sheet.close(this.$f7.$('#share-sheet'));
        },
        handleClick(target, index) {
            const { type } = target;
            if (type) this.handleShare(type);
            else this.handleSave();
        },
        async handleShare(val) {
            try {
                const { market_id: marketId } = getUrlParams();
                let { host } = window.location;
                // 正式环境分享都切换成备份域名
                if (getENV() === 'prod' && val !== 'TT') host = 'cdn.i52tt.com';
                let url = `${window.location.protocol}//${host}${window.location.pathname}?immersion=1`;
                if (val !== 'TT') url += `&market_id=${marketId || 0}`;
                console.log(url);
                const shareData = {
                    share_type: val,
                    ttShareMsgType: 0, // 1图片，其他（图文）0 默认
                    title: '2025年度盛典赛事名人堂',
                    content: '年度盛典群雄荟萃，谁是无冕之王',
                    url: `${url}`, // 图文调整链接
                    // url:url, // 图文调整链接
                    imageUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20251224110314_92468145.png', // 图文里的缩略图url
                    imagePath: '', // 纯图片类型（仅支持手机本地地址）
                    label: '', // 入口标签名
                    musicUrl: '',
                };
                this.closeSheet();
                // myWebview.trackEvent('分享到','TT/微信/朋友圈/QQ/QQ空间')

                if (this.$f7.device.ios) {
                    try {
                        const version = TTJSBridge.invoke('operate', 'getVersion');
                        if (version < '3.6.2') {
                            f7toast.showInfo('当前客户端版本不支持该操作，请升级最新版本');
                            return;
                        }
                    } catch (e) {
                        // eslint-disable-next-line
                        alert(e);
                    }
                }

                // 安卓先检查是否安装APP
                if (!this.$f7.device.ios) {
                    if (['Wechat', 'WechatMoments'].includes(val)) {
                        if (!checkInstallApp('Wechat')) {
                            f7toast.showInfo('分享失败，未安装微信');
                            return;
                        }
                    }
                    if (['QQ', 'QZone'].includes(val)) {
                        if (!checkInstallApp('QQ')) {
                            f7toast.showInfo('分享失败，未安装QQ');
                            return;
                        }
                    }
                }

                TTJSBridge.invoke('ui', 'thirdPlatformShare', JSON.stringify(shareData)); // 新接口
            } catch (e) {
                // eslint-disable-next-line
                alert(e);
            }
        },
    },
};
</script>
