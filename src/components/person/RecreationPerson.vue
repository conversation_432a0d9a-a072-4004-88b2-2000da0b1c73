<template>
    <div class="section-item section-bg-guide">
        <div class="relative">
            <Rank-title class="mt-[40px]">娱乐个人榜</Rank-title>
            <Rank-right-tab current-tab="RecreationPerson" />
            <div class="person-tab">
                <div
                    :class="['person-tab-item', { 'is-special': item.isSpecial, 'is-active': currentTab === item.value }]"
                    v-for="(item, index) in tabList"
                    :key="`person-tab-item-${index}`"
                    @click="changeTab(item.value)"
                >
                    <p class="title-text">{{ item.title }}</p>
                </div>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 'RecreationMvp',
            tabList: [
                {
                    title: 'MVP',
                    value: 'RecreationMvp',
                    isSpecial: true,
                },
                {
                    title: '踏浪榜TOP10',
                    value: 'StarPerson',
                },
                {
                    title: '逐风榜TOP10',
                    value: 'FirePerson',
                },
                {
                    title: '擎天榜TOP10',
                    value: 'WindPerson',
                },
                {
                    title: '裂空榜TOP10',
                    value: 'SkyPerson',
                },
            ],
        };
    },
    methods: {
        changeTab(value) {
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.person-tab {
    margin-top: 25px;
}

.person-tab-item {
    margin: 14px auto 0 auto;
    padding-top: 25px;
    width: 238px;
    height: 74px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    >.title-text {
        font-size: 22px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg, #451908 0%, #d7611a 100%));
        line-height: 23px;
        text-align: center;
    }
}

.person-tab-item+.person-tab-item {
    margin-top: 7px;
}
</style>
