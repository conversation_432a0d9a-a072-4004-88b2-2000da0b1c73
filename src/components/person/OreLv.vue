<template>
    <div class="section-item section-bg">
        <Nav-btn type="OrePerson" />
        <Rank-details-content
            ref="rankDetailsContent"
            rank-title="绿晶TOP3"
            rank-type-text="晶矿代言人"
            :rank-list="rankList"
            :is-guild="false"
            id="OreLv"
        />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.oreRank?.lv || [];
        },
    },
};
</script>
