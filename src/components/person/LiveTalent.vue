<template>
    <div class="section-item section-bg-guide">
        <div class="relative">
            <Rank-title class="mt-[38px] mb-[-12px]">听听骑士榜</Rank-title>
            <div
                class="person-tab-item is-live"
                @click="changeTab(knightTab.value,true)"
            >
                <p class="title-text">{{ knightTab.title }}</p>
            </div>
            <Rank-title :show-icon="false">听听才艺榜</Rank-title>
            <Rank-right-tab current-tab="LiveTalent" />
            <div class="person-tab">
                <div
                    class="person-tab-item is-live"
                    :class="{'is-hidden': !item.visible}"
                    v-for="(item, index) in tabList"
                    :key="`person-tab-item-${index}`"
                    @click="changeTab(item.value, item.visible)"
                >
                    <p class="title-text">{{ item.title }}</p>
                </div>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
import config from '@/config';
import { showToast } from '@/utils/utils';

export default {
    data() {
        return {
            currentTab: 'LiveTalent',
            knightTab: {
                title: '骑士榜TOP10',
                value: 'Knight',
            },
            tabList: [
                {
                    title: '音乐赛区TOP3',
                    value: 'MusicTalent',
                },
                {
                    title: '情感赛区TOP3',
                    value: 'EmotionTalent',
                },
                {
                    title: '脱口秀赛区TOP3',
                    value: 'TalkTalent',
                },
                {
                    title: '二次元赛区TOP3',
                    value: 'AnimeTalent',
                },
            ].map((item) => ({
                ...item,
                visible: config.visiblePages.includes(item.value),
            })),
        };
    },
    methods: {
        changeTab(value, visible) {
            if (!visible) {
                showToast('【听听赛】获奖名单将在 2025-07-26 00:00:00 时间更新，尽情留意！');
                return;
            }
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.person-tab {
    // margin-top: 25px;
}

.person-tab-item {
    margin: 0 auto;
    padding-top: 25px;
    width: 238px;
    height: 73.5px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    &.is-live {
        background-image: url('/static/img/<EMAIL>');
    }

    &.is-cp {
        background-image: url('/static/img/<EMAIL>');
    }

    >.title-text {
        font-size: 22px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg, #451908 0%, #d7611a 100%));
        line-height: 23px;
        text-align: center;
    }
}

.person-tab-item+.person-tab-item {
    margin-top: 7px;
}
</style>
