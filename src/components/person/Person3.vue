<template>
    <div class="section-item section-bg">
        <Nav-btn type="YulePerson" />
        <Rank-details-content
            ref="rankDetailsContent"
            rank-title="灼曜之境送礼总榜TOP10"
            rank-type-text="娱乐大人物"
            :rank-list="rankList"
            :is-guild="false"
            id="Person3"
        />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.person3 || [];
        },
    },
};
</script>
