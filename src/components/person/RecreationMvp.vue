<template>
    <div class="section-item section-bg">
        <Nav-btn type="RecreationPerson" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-type-text="娱乐个人榜"
                              rank-title="MVP"
                              :rank-list="rankList"
                              :is-guild="false"
                              :is-mvp="true"
                              id="RecreationMvp" />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.personRank?.mvp || [];
        },
    },
};
</script>
