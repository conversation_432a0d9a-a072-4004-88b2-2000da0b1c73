<template>
    <div class="section-item section-bg-guide">
        <div class="relative">
            <Rank-title class="mt-[40px]">听听达人榜</Rank-title>
            <Rank-right-tab current-tab="LivePerson" />
            <div class="person-tab">
                <div class="person-tab-item is-live"
                     v-for="(item,index) in tabList"
                     :key="`person-tab-item-${index}`"
                     @click="changeTab(item.value)">
                    <p class="title-text">{{item.title}}</p>
                </div>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 'LiveMvp',
            tabList: [
                {
                    title: 'MVP达人',
                    value: 'LiveMvp',
                    isSpecial: true,
                },
                {
                    title: '音乐赛道TOP10',
                    value: 'MusicPerson',
                },
                {
                    title: '情感赛道TOP10',
                    value: 'Emotion<PERSON>erson',
                },
                {
                    title: '才艺赛道TOP10',
                    value: 'TalentPerson',
                },
                {
                    title: '新星赛道TOP10',
                    value: 'NewStarPerson',
                },
            ],
        };
    },
    methods: {
        changeTab(value) {
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.person-tab {
    margin-top: 25px;
}
.person-tab-item {
    margin: 12px auto 0 auto;
    padding-top: 25px;
    width: 238px;
    height: 73.5px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    &.is-live {
        background-image: url('/static/img/<EMAIL>');
    }

    > .title-text {
        font-size: 22px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg,#451908 0%, #d7611a 100%));
        line-height: 23px;
        text-align: center;
    }
}
.person-tab-item + .person-tab-item {
    margin-top: 7px;
}
</style>
