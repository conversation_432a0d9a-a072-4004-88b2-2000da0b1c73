<template>
    <div class="section-item section-bg">
        <Nav-btn type="LiveTalent" />
        <Rank-details-content
            ref="rankDetailsContent"
            rank-title="脱口秀赛区TOP3"
            rank-type-text="听听才艺赛"
            :rank-list="rankList"
            :is-recreation="false"
            :is-guild="false"
            id="TalkTalent"
        />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            // 赛区 1:音乐 2:情感 3:二次元 4:脱口秀
            return this.$store.state.liveTalentRank[4] || [];
        },
    },
};
</script>
