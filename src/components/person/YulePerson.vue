<template>
    <div class="section-item section-bg-guide">
        <div class="relative">
            <Rank-title class="mt-[50px]">娱乐大人物</Rank-title>
            <Rank-right-tab current-tab="Yule<PERSON>erson" />
            <div class="person-tab">
                <div
                    :class="['person-tab-item']"
                    v-for="(item, index) in tabList"
                    :key="`person-tab-item-${index}`"
                    @click="changeTab(item.value)"
                >
                    <p class="desc-text">{{ item.desc }}</p>
                    <p class="title-text">{{ item.title }}</p>
                </div>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
import config from '@/config';

export default {
    data() {
        return {
            currentTab: 'YulePerson',
        };
    },
    computed: {
        tabList() {
            const list = [
                {
                    title: '至尊神壕榜TOP10',
                    desc: '浮光圣殿',
                    value: 'Person1',
                },
                {
                    title: '送礼总榜TOP10',
                    desc: '倾世恋歌',
                    value: 'Person2',
                },
                {
                    title: '送礼总榜TOP10',
                    desc: '灼曜之境',
                    value: 'Person3',
                },
                {
                    title: '送礼总榜TOP10',
                    desc: '金域传说',
                    value: 'Person4',
                },
                {
                    title: '送礼总榜TOP10',
                    desc: '永恒誓约',
                    value: 'Person5',
                },
            ];
            if (!this.isRevenueBegin) {
                return list.filter((item) => config.visiblePages.indexOf(item.value) !== -1);
            }
            return list;
        },
    },
    methods: {
        changeTab(value) {
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.person-tab {
    margin-top: 30px;
    min-height: 330px;
}

.person-tab-item {
    margin: 0 auto;
    padding-top: 19px;
    width: 238px;
    height: 73.5px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    >.title-text {
        margin-top: -5px;
        font-size: 17px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg, #451908 0%, #d7611a 100%));
        line-height: 23px;
        text-align: center;
    }

    >.desc-text {
        font-size: 11px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg, #451908 0%, #d7611a 100%); );
        line-height: 17px;
        text-align: center;
    }
}

.person-tab-item+.person-tab-item {
    margin-top: 7px;
}
</style>
