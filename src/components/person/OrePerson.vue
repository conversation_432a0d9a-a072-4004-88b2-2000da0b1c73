<template>
    <div class="section-item section-bg-guide">
        <div class="relative">
            <Rank-title>晶矿代言人</Rank-title>
            <Rank-right-tab current-tab="OrePerson" />
            <div class="person-tab">
                <div
                    class="person-tab-item"
                    v-for="(item, index) in tabList"
                    :class="['ore-hj2','ore-hj', 'ore-bj', 'ore-lj', 'ore-lv', 'ore-zj'][index]"
                    :key="`person-tab-item-${index}`"
                    @click="changeTab(item.value)"
                >
                    <p class="title-text">{{ item.title }}</p>
                </div>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 'OrePerson',
            tabList: [
                {
                    title: '红晶TOP3',
                    value: 'OreHong',
                },
                {
                    title: '黄晶TOP3',
                    value: 'Ore<PERSON>uang',
                },
                {
                    title: '白晶TOP3',
                    value: 'OreBai',
                },
                {
                    title: '蓝晶TOP3',
                    value: 'OreLan',
                },
                {
                    title: '绿晶TOP3',
                    value: 'OreLv',
                },
                {
                    title: '紫晶TOP3',
                    value: 'OreZi',
                },
            ],
        };
    },
    methods: {
        changeTab(value) {
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.person-tab {
    // margin-top: 25px;
}

.person-tab-item {
    margin: 0 auto;
    padding-top: 25px;
    width: 238px;
    height: 73.5px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    &.ore-hj2{
        background-image: url('/static/img/<EMAIL>');
    }

    &.ore-hj{
        background-image: url('/static/img/<EMAIL>');
    }

    &.ore-bj{
        background-image: url('/static/img/<EMAIL>');
    }
    &.ore-lj{
        background-image: url('/static/img/<EMAIL>');
    }
    &.ore-lv{
        background-image: url('/static/img/<EMAIL>');
    }
    &.ore-zj{
        background-image: url('/static/img/<EMAIL>');
    }

    >.title-text {
        font-size: 22px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg, #e6a528 0%, #fff6e0 100%));
        line-height: 23px;
        text-align: center;
    }
}

.person-tab-item+.person-tab-item {
    margin-top: 7px;
}
</style>
