<template>
    <div class="section-item section-bg">
        <Nav-btn type="RecreationPerson" />
        <Rank-details-content
            ref="rankDetailsContent"
            rank-title="踏浪榜TOP10"
            rank-type-text="娱乐个人榜"
            :rank-list="rankList"
            :is-guild="false"
            id="StarPerson"
        />
        <Arrow-btn />
    </div>
</template>

<script setup>
// src/components/person/StarPerson.vue
import { computed } from 'vue';
import { usePersonRankStore } from '@/stores/modules/usePersonRankStore';

// 使用 Pinia store
const personRankStore = usePersonRankStore();

// 响应式数据
const rankList = computed(() => personRankStore.getStarRank());
</script>
