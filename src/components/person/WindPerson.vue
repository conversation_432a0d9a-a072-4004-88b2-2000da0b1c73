<template>
    <div class="section-item section-bg">
        <Nav-btn type="RecreationPerson" />
        <Rank-details-content
            ref="rankDetailsContent"
            rank-title="擎天榜TOP10"
            rank-type-text="娱乐个人榜"
            :rank-list="rankList"
            :is-guild="false"
            id="WindPerson"
        />
        <Arrow-btn />
    </div>
</template>

<script>
// 页面加载时请求接口 获取数据存在组件中
// yuleRequest
export default {
    computed: {
        rankList() {
            return this.$store.state.personRank?.wind || [];
        },
    },
};
</script>
