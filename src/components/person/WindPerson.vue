<template>
    <div class="section-item section-bg">
        <Nav-btn type="RecreationPerson" />
        <Rank-details-content
            ref="rankDetailsContent"
            rank-title="擎天榜TOP10"
            rank-type-text="娱乐个人榜"
            :rank-list="rankList"
            :is-guild="false"
            id="WindPerson"
        />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.personRank?.sky || [];
        },
    },
};
</script>
