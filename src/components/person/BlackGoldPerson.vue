<template>
    <div class="section-item section-bg">
        <Nav-btn type="RecreationOther" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="大人物榜TOP10"
                              rank-type-text="黑金大人物榜"
                              :rank-list="rankList"
                              :is-guild="false"
                              id="BlackGoldPerson" />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.blackGoldRank || [];
        },
    },
};
</script>
