<template>
    <div class="section-item section-bg">
        <Nav-btn type="LivePerson" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="新星赛道TOP10"
                              rank-type-text="听听达人榜"
                              :rank-list="rankList"
                              :is-recreation="false"
                              :is-guild="false"
                              id="NewStarPerson" />
        <Arrow-btn v-if="isRevenueBegin" />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.liveAnchorRank.newstar || [];
        },
    },
};
</script>
