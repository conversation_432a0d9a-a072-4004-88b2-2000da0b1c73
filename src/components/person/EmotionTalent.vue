<template>
    <div class="section-item section-bg">
        <Nav-btn type="LiveTalent" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="情感赛道TOP6"
                              rank-type-text="听听才艺赛"
                              :rank-list="rankList"
                              :is-recreation="false"
                              :is-guild="false"
                              id="EmotionTalent" />
        <Arrow-btn />
    </div>
</template>

<script>
// src/components/person/EmotionTalent.vue
import { useLiveTalentRankStore } from '@/stores/modules/useLiveTalentRankStore';

export default {
    computed: {
        rankList() {
            // 赛区 1:音乐 2:情感 3:二次元 4:脱口秀
            const liveTalentRankStore = useLiveTalentRankStore();
            return liveTalentRankStore.getEmotionTalentRank();
        },
    },
};
</script>
