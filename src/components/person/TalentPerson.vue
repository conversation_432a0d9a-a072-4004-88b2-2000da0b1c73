<template>
    <div class="section-item section-bg">
        <Nav-btn type="LivePerson" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="才艺赛道TOP10"
                              rank-type-text="听听达人榜"
                              :rank-list="rankList"
                              :is-recreation="false"
                              :is-guild="false"
                              id="TalentPerson" />
        <Arrow-btn />
    </div>
</template>

<script setup>
// src/components/person/TalentPerson.vue
import { computed } from 'vue';
import { useLiveAnchorRankStore } from '@/stores/modules/useLiveAnchorRankStore';

// 使用 Pinia store
const liveAnchorRankStore = useLiveAnchorRankStore();

// 响应式数据
const rankList = computed(() => liveAnchorRankStore.getTalentRank());
</script>
