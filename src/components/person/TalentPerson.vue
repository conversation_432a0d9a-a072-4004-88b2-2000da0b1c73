<template>
    <div class="section-item section-bg">
        <Nav-btn type="LivePerson" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="才艺赛道TOP10"
                              rank-type-text="听听达人榜"
                              :rank-list="rankList"
                              :is-recreation="false"
                              :is-guild="false"
                              id="TalentPerson" />
        <Arrow-btn />
    </div>
</template>

<script>
// src/components/person/TalentPerson.vue
import { useLiveAnchorRankStore } from '@/stores/modules/useLiveAnchorRankStore';

export default {
    computed: {
        rankList() {
            const liveAnchorRankStore = useLiveAnchorRankStore();
            return liveAnchorRankStore.getTalentRank();
        },
    },
};
</script>
