<template>
    <div class="section-item section-bg">
        <Nav-btn type="LivePerson" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="音乐赛道TOP10"
                              rank-type-text="听听达人榜"
                              :rank-list="rankList"
                              :is-recreation="false"
                              :is-guild="false"
                              id="MusicPerson" />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.liveAnchorRank.music || [];
        },
    },
};
</script>
