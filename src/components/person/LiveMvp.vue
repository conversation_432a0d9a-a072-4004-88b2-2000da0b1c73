<template>
    <div class="section-item section-bg">
        <Nav-btn type="LivePerson" />
        <Rank-details-content
            ref="rankDetailsContent"
            rank-type-text="听听达人榜"
            rank-title="MVP达人"
            :rank-list="rankList"
            :is-recreation="false"
            :is-guild="false"
            :is-mvp="true"
            id="LiveMvp"
        />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.liveAnchorRank?.mvp || [];
        },
    },
};
</script>
