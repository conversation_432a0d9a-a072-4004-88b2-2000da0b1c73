<template>
    <div class="section-item section-bg">
        <Nav-btn type="LivePerson" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-type-text="听听达人榜"
                              rank-title="MVP达人"
                              :rank-list="rankList"
                              :is-recreation="false"
                              :is-guild="false"
                              :is-mvp="true"
                              id="LiveMvp" />
        <Arrow-btn />
    </div>
</template>

<script setup>
// src/components/person/LiveMvp.vue
import { computed } from 'vue';
import { useLiveAnchorRankStore } from '@/stores/modules/useLiveAnchorRankStore';

// 使用 Pinia store
const liveAnchorRankStore = useLiveAnchorRankStore();

// 响应式数据
const rankList = computed(() => liveAnchorRankStore.getMvpRank());
</script>
