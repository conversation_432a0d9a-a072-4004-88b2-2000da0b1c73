<template>
    <div class="section-item section-bg">
        <Nav-btn type="QiangTing" />
        <Room-details-content ref="rankDetailsContent"
                              rank-title="游戏TOP10"
                              rank-type-text="娱乐强厅赛"
                              :rank-list="rankList"
                              id="QiangTingGame" />
        <Arrow-btn />
    </div>
</template>

<script>
import RoomDetailsContent from '@/components/common/room-details-content';

export default {
    components: {
        RoomDetailsContent,
    },
    computed: {
        rankList() {
            return this.$store.state.qiangTingRank?.game || [];
        },
    },
};
</script>
