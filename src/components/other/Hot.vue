<template>
    <div class="section-item section-bg">
        <Nav-btn type="HotOther" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="人气榜TOP10"
                              rank-type-text="年度人气榜"
                              :rank-list="rankList"
                              :is-guild="false"
                              id="Hot" />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.hotRank || [];
        },
    },
};
</script>
