<template>
    <div class="section-item section-bg-guide relative z-[1]">
        <div class="relative">
            <Rank-right-tab current-tab="HotOther" />
            <template v-for="(item,index) of tabList">
                <Rank-title class="mb-[-5px]"
                            :class="{'mt-[50px]': index === 0, 'mt-[38px]': index !== 0}"
                            :show-icon="index === 0"
                            :key="`rank-title-${item.value}`">{{item.desc}}</Rank-title>
                <div class="rich-tab-item"
                     :class="{'mb-[12px]': index !== 0, 'is-live': item.isLive}"
                     :key="`rich-tab-item-${item.value}`"
                     @click="changeTab(item.value)">
                    <p class="title-text">{{item.title}}</p>
                </div>
            </template>
            <!-- <Rank-title class="mt-[50px] mb-[-5px]">年度人气榜</Rank-title>
            <div class="rich-tab-item"
                 @click="changeTab(hotTab.value)">
                <p class="title-text">{{hotTab.title}}</p>
            </div> -->
            <!-- <Rank-title class="mt-[38px] mb-[-12px]"
                        :show-icon="false">听听人气榜</Rank-title>
            <div class="rich-tab-item is-live"
                 @click="changeTab(liveHotTab.value)">
                <p class="title-text">{{liveHotTab.title}}</p>
            </div>
            <Rank-title class="mt-[38px] mb-[-12px]"
                        :show-icon="false">听听骑士榜</Rank-title>
            <div class="rich-tab-item is-live"
                 @click="changeTab(knightTab.value)">
                <p class="title-text">{{knightTab.title}}</p>
            </div> -->
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
import config from '../../config';

export default {
    data() {
        return {
            currentTab: 'HotOther',
            hotTab: {
                title: '人气榜TOP10',
                value: 'Hot',
            },
            liveHotTab: {
                title: '人气榜TOP10',
                value: 'LiveHot',
            },
            knightTab: {
                title: '骑士榜TOP10',
                value: 'Knight',
            },
            // hotTab: {
            //     title: '人气榜TOP10',
            //     value: 'Hot',
            // },
        };
    },
    computed: {
        tabList() {
            const list = [
                {
                    title: '人气榜TOP10',
                    desc: '年度人气榜',
                    value: 'Hot',
                },
                {
                    title: '人气榜TOP10',
                    desc: '听听人气榜',
                    value: 'LiveHot',
                    isLive: true,
                },
                {
                    title: '骑士榜TOP10',
                    desc: '听听骑士榜',
                    value: 'Knight',
                    isLive: true,
                },
            ];
            if (!this.isRevenueBegin) {
                return list.filter((item) => config.visiblePages.indexOf(item.value) !== -1);
            }
            return list;
        },
    },
    methods: {
        changeTab(value) {
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.rich-tab-item {
    margin: 0 auto;
    padding-top: 26px;
    width: 238px;
    height: 73.5px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    &.is-live {
        background-image: url('/static/img/<EMAIL>');
    }
    &.is-cp {
        background-image: url('/static/img/<EMAIL>');
    }
    > .title-text {
        font-size: 22px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg,#451908 0%, #d7611a 100%));
        line-height: 23px;
        text-align: center;
    }
}
.rich-tab-item + .rich-tab-item {
    margin-top: 7px;
}
</style>
