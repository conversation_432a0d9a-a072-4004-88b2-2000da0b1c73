<template>
    <div class="section-item section-bg">
        <Nav-btn type="RecreationOther" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="神壕榜TOP10"
                              rank-type-text="年度神壕榜"
                              :rank-list="rankList"
                              :is-guild="false"
                              :is-rich="true"
                              id="Rich" />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.richRank || [];
        },
    },
};
</script>
