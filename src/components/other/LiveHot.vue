<template>
    <div class="section-item section-bg">
        <Nav-btn type="HotOther" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="人气榜TOP10"
                              rank-type-text="听听人气榜"
                              :is-recreation="false"
                              :rank-list="rankList"
                              :is-guild="false"
                              id="LiveHot" />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.liveHotRank || [];
        },
    },
};
</script>
