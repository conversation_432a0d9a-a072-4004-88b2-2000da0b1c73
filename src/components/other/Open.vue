<template>
    <div class="section-item open">
        <img :class="['open-img',{'more-top':!showStatusBar }]"
             :src="require('/static/img/<EMAIL>')" />
        <!-- <img class="absolute top-[99px] right-[0px] w-[54px] h-[28px]"
             @click="jumpPage('revenue-annual-ceremony-2023')"
             :src="require('/static/img/<EMAIL>')" />
        <img class="absolute top-[135px] right-[0px] w-[54px] h-[28px]"
             @click="jumpPage('live-annual-festival-2023')"
             :src="require('/static/img/<EMAIL>')" /> -->
        />
        <img :src="require('/static/img/<EMAIL>')"
             class="absolute top-[90px] left-[13px] w-[56px] h-[56px]"
             @click="jumpMainEntry" />
        <Arrow-btn />
    </div>
</template>

<script>
import { parseUrlQuery, jumpLink } from '@/utils/webview-init';
import { mainEntry } from '@/links';
import { baseUrl } from '../../config/url';

export default {
    methods: {
        jumpPage(projectName) {
            const { isDev } = parseUrlQuery();
            if (!window.myWebview.isInApp() && !isDev) {
                window.activeApp.open();
                return;
            }
            const url = `${baseUrl(true)}${projectName}/index.html?immersion=1`;
            if (this.isInApp) {
                jumpLink(url);
            } else {
                window.location.href = url;
            }
        },
        jumpMainEntry() {
            const { isDev } = parseUrlQuery();
            if (!window.myWebview.isInApp() && !isDev) {
                window.activeApp.open();
                return;
            }
            if (this.isInApp) {
                jumpLink(mainEntry);
            } else {
                window.location.href = mainEntry;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.open {
    position: relative;
    background-color: #131112;

    .open-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        &.more-top {
            top: -50px;
        }
    }
}
</style>
