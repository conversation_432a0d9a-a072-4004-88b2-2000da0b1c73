<template>
    <div class="section-item open">
        <img
            :class="['open-img', { 'more-top': !showStatusBar }]"
            :src="require('/static/img/<EMAIL>')"
        />
        <!-- <img class="absolute top-[99px] right-[0px] w-[54px] h-[28px]"
             @click="jumpPage('revenue-annual-ceremony-2023')"
             :src="require('/static/img/<EMAIL>')" />
        <img class="absolute top-[135px] right-[0px] w-[54px] h-[28px]"
             @click="jumpPage('live-annual-festival-2023')"
             :src="require('/static/img/<EMAIL>')" /> -->
        />

        <img
            v-show="!showLottie"
            class="absolute top-[82px] left-[13px] w-[350px] h-[542px]"
            :src="require('/static/img/<EMAIL>')"
            alt=""
        >
        <div
            id="lottieId"
            class="absolute top-[82px] left-[13px] w-[350px] h-[542px]"
        >
        </div>
           <img
            :src="require('/static/img/<EMAIL>')"
            class="absolute top-[90px] left-[13px] w-[56px] h-[56px] z-1"
            @click="jumpMainEntry"
        />
        <Arrow-btn />
    </div>
</template>

<script>
import { parseUrlQuery, jumpLink } from '@/utils/webview-init';
import { mainEntry } from '@/links';
// eslint-disable-next-line import/no-extraneous-dependencies
import Lottie from 'lottie-web';
import { baseUrl } from '../../config/url';

export default {
    data() {
        return {
            showLottie: false,
        };
    },
    mounted() {
        this.initLottie();
    },
    methods: {
        jumpPage(projectName) {
            const { isDev } = parseUrlQuery();
            if (!window.myWebview.isInApp() && !isDev) {
                window.activeApp.open();
                return;
            }
            const url = `${baseUrl(true)}${projectName}/index.html?immersion=1`;
            if (this.isInApp) {
                jumpLink(url);
            } else {
                window.location.href = url;
            }
        },
        initLottie() {
            Lottie.loadAnimation({
                container: document.getElementById('lottieId'),
                renderer: 'svg',
                loop: true,
                autoplay: true,
                path: 'https://obs-cdn.52tt.com/tt/fe-moss/web/mrt-2507/20250714155606_15715486.json',
                complete: () => {
                    this.showLottie = true;
                },
            });
        },
        jumpMainEntry() {
            const { isDev } = parseUrlQuery();
            if (!window.myWebview.isInApp() && !isDev) {
                window.activeApp.open();
                return;
            }
            if (this.isInApp) {
                jumpLink(mainEntry);
            } else {
                window.location.href = mainEntry;
            }
        },
    },
};
</script>

<style lang="less" scoped>
.open {
    position: relative;
    background-color: #090907;

    .open-img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;

        &.more-top {
            top: -50px;
        }
    }
}
</style>
