<template>
    <div class="section-item section-bg">
        <Nav-btn type="RecreationOther" />
        <div class="cp-rank-content">
            <div class="relative">
                <img class="absolute left-[34px] top-[-8pxpx] w-[38px] h-[39px]"
                     :src="require('/static/img/<EMAIL>')" />
                <img class="absolute right-[34px] top-[126px] w-[35px] h-[36px]"
                     :src="require('/static/img/<EMAIL>')" />
                <img class="m-center w-[178px] h-[24px]"
                     :src="require('/static/img/<EMAIL>')" />
                <!-- <img class="m-center mt-[2px] w-[174px] h-[35px]"
                     :src="require('/static/img/<EMAIL>')" /> -->
            </div>
            <p class="rank-title">年度CP榜</p>
            <img class="m-center mt-[-25px] w-[374px] h-[50px]"
                 :src="require('/static/img/<EMAIL>')" />
            <div class="relative"
                 v-if="rankList && rankList.length">
                <div v-for="(item,index) in rankList"
                     :key="`swiper-slide-${index}`"
                     class="slide">
                    <div class="ranking">
                        <!-- <img class="arrow-img"
                             :src="require('/static/img/<EMAIL>')" /> -->
                        <p class="title-text">
                            {{rankingText[item.ranking || item?.rank]}}
                        </p>
                        <!-- <img class="arrow-img"
                             :src="require('/static/img/<EMAIL>')" /> -->
                    </div>
                    <div class="cp-rank-item">
                        <div class="left-cp mr-[-6px]">
                            <div class="relative">
                                <img class="user-head"
                                     @click="handleAvatar(item.userInfo.username)"
                                     :src="item.userInfo.username | head" />
                                <!-- <Room-status :item="item?.channelInfo" /> -->
                            </div>
                            <p class="nickname">{{item.userInfo.nickname}}</p>
                            <p class="user-id">ID:{{item.userInfo.alias}}</p>
                        </div>
                        <div class="right-cp">
                            <div class="relative">
                                <img class="user-head"
                                     @click="handleAvatar(item.cpInfo?.userInfo?.username)"
                                     :src="item.cpInfo?.userInfo?.username | head" />
                                <!-- <Room-status :item="item.cpInfo?.channelInfo" /> -->
                            </div>
                            <p class="nickname">{{item.cpInfo?.userInfo?.nickname}}</p>
                            <p class="user-id">ID:{{item.cpInfo?.userInfo?.alias}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="relative z-[1] mt-[2vh] h-[62px]">
                <div :class="['rank-list-box',{'is-hide':!isOpen}]">
                    <img class="cove-img left-[-1px]"
                         :src="require('/static/img/<EMAIL>')" />
                    <div :class="['rank-list',{'justify-center':rankList.length<=2}]"
                         ref="rankList">
                        <div v-for="(item,index) in rankList"
                             @click="changeActiveIndex(index)"
                             :key="`cp-head-${index}`"
                             ref="head"
                             class="cp-head-box shrink-0">
                            <img :class="['cp-head',{'is-active':activeIndex===index}]"
                                 :src="item.userInfo?.username | head" />
                            <img :class="['cp-head',{'is-active':activeIndex===index}]"
                                 :src="item.cpInfo?.userInfo?.username | head" />
                        </div>
                    </div>
                    <img class="cove-img right-[-1px]"
                         :src="require('/static/img/<EMAIL>')" />
                </div>
                <div class="operate-btn">
                    <img v-show="isOpen"
                         @click="closeRankList"
                         :src="require('/static/img/<EMAIL>')" />
                    <img v-show="!isOpen"
                         @click="openRankList"
                         :src="require('/static/img/<EMAIL>')" />
                </div>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
import config from '../../config';
import { scrollIntoViewMiddle } from '../../utils/utils';

export default {
    data() {
        return {
            rankingText: Object.freeze(config.rankingText),
            activeIndex: 0,
            isOpen: true,
            arrowOldDisplay: {
                prev: '',
                next: '',
            },
            // rewardSwiper: null,
            // swiperParams: {
            //     navigation: {
            //         nextEl: '.button-next-cp',
            //         prevEl: '.button-prev-cp',
            //     },
            // },
        };
    },
    computed: {
        rankList() {
            return this.$store.state.cpRank || [];
        },
    },
    watch: {
        activeIndex(newVal) {
            this.$nextTick(() => {
                if (this.$refs.rankList && this.$refs.head[newVal]) {
                    scrollIntoViewMiddle(this.$refs.head[newVal], this.$refs.rankList);
                }
            });
        },
    },

    mounted() {
        // this.$watch('rankList', async (val) => {
        //     if (val && val.length && !this.rewardSwiper) {
        //         await this.$nextTick();
        //         this.rewardSwiper = this.$f7.swiper.get(this.$refs.rankSwiper.$el);
        //         this.rewardSwiper.on('slideChange', () => {
        //             this.activeIndex = this.rewardSwiper.activeIndex;
        //         });
        //     }
        // });
    },
    methods: {
        changeActiveIndex(value) {
            this.activeIndex = value;
            this.$fp.moveTo('Cp', value);
        },
        changeSlideStatus(value) {
            this.activeIndex = value;
            if (!this.isOpen) {
                if (value === 0) {
                    this.arrowOldDisplay.prev = 'none';
                    this.arrowOldDisplay.next = 'block';
                } else if (value === this.rankList.length - 1) {
                    this.arrowOldDisplay.prev = 'block';
                    this.arrowOldDisplay.next = 'none';
                } else {
                    this.arrowOldDisplay.prev = 'block';
                    this.arrowOldDisplay.next = 'block';
                }
            }
        },
        closeRankList() {
            this.isOpen = false;
            // 暂存当前左右切换箭头的状态
            const arrowPrev = document.querySelector('.section.active .fp-arrow.fp-prev');
            this.arrowOldDisplay.prev = arrowPrev.style.display;
            const arrowNext = document.querySelector('.section.active .fp-arrow.fp-next');
            this.arrowOldDisplay.next = arrowNext.style.display;
            arrowPrev.style.display = 'none';
            arrowNext.style.display = 'none';
        },
        openRankList() {
            this.isOpen = true;
            const arrowPrev = document.querySelector('.section.active .fp-arrow.fp-prev');
            const arrowNext = document.querySelector('.section.active .fp-arrow.fp-next');
            arrowPrev.style.display = this.arrowOldDisplay.prev;
            arrowNext.style.display = this.arrowOldDisplay.next;
        },
    },
};
</script>

<style lang="less" scoped>
.cp-rank-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 50px;
    height: 100vh;
}
.rank-title {
    margin-top: 1.5vh;
    font-size: 40px;
    line-height: 50px;
    font-family: 'HYLingXinSquare';
    .text-gradient(linear-gradient(0deg,#f9bc17 0%, #fff2c3 100%));
    text-align: center;
}

#rankSwiper {
    width: 100%;
}
.ranking {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: -1vh;
    .arrow-img {
        width: 16px;
        height: 16px;
    }
    .title-text {
        font-size: 45px;
        line-height: 53px;
        font-family: 'HYLingXinSquare';
        text-align: center;
        .text-gradient(linear-gradient(0deg,#f9bc17 0%, #fff2c3 100%));
    }
}
.cp-rank-item {
    display: flex;
    justify-content: center;
    margin: 1.5vh auto 0;
    .left-cp,
    .right-cp {
        padding-top: 34px;
        width: 159px;
        height: 220px;
        .full-bg('../../../static/img/<EMAIL>');
    }
    .right-cp {
        margin-left: 3px;
    }
    .user-head {
        margin-left: 40px;
        .user-head(79px);
    }
    .nickname {
        margin: 24px auto 0;
        width: 95px;
        font-size: 16px;
        font-weight: bold;
        line-height: 22px;
        text-align: center;
        .text-gradient(linear-gradient(0deg,#f9bc17 0%, #fff2c3 100%));
        .one-line();
    }
    .user-id {
        margin-top: 1px;
        font-size: 11px;
        line-height: 17px;
        text-align: center;
        .text-gradient(linear-gradient(0deg,#f9bc17 0%, #fff2c3 100%));
    }
}
/deep/ .fp-controlArrow {
    top: 170px;
}
.rank-list-box {
    display: flex;
    align-items: center;
    position: relative;
    margin: 0 auto;
    width: 304px;
    height: 61px;
    .full-bg('/static/img/<EMAIL>');
    &.is-hide {
        visibility: hidden;
    }
}
.cove-img {
    position: absolute;
    top: -1px;
    width: 34px;
    height: 62px;
}
.rank-list {
    display: flex;
    // padding-left: 20px;
    // padding-right: 20px;
    width: 100%;
    overflow-x: auto;
    overflow-y: hidden;
    &::after {
        content: '';
        padding-right: 20px;
    }

    &::before {
        content: '';
        padding-left: 20px;
    }
    .cp-head-box {
        flex-shrink: 0;
        display: flex;
    }
    .cp-head-box + .cp-head-box {
        margin-left: 19px;
    }
    .cp-head {
        .user-head(37px);
        &.is-active {
            border: 2px solid #efd053;
        }
    }
    .cp-head + .cp-head {
        margin-left: 6px;
    }
}
.operate-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 0;
    width: 17px;
    height: 49px;
}
</style>
