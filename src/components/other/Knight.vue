<template>
    <div class="section-item section-bg">
        <Nav-btn type="LiveTalent" />
        <Rank-details-content ref="rankDetailsContent"
                              rank-title="骑士榜TOP10"
                              rank-type-text="听听骑士榜"
                              :is-recreation="false"
                              :rank-list="rankList"
                              :is-guild="false"
                              id="Knight" />
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    computed: {
        rankList() {
            return this.$store.state.liveKnightRank || [];
        },
    },
};
</script>
