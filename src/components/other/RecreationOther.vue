<template>
    <div class="section-item section-bg-guide relative z-[1]">
        <div class="relative">
            <Rank-right-tab current-tab="RecreationOther" />
            <Rank-title class="mt-[50px] mb-[-12px]">年度神壕榜</Rank-title>
            <div class="rich-tab-item"
                 @click="changeTab(richTab.value)">
                <p class="title-text">{{richTab.title}}</p>
            </div>
            <Rank-title class="mt-[38px] mb-[-12px]"
                        :show-icon="false">黑金大人物榜</Rank-title>
            <div class="rich-tab-item is-live"
                 @click="changeTab(blackGoldTab.value)">
                <p class="title-text">{{blackGoldTab.title}}</p>
            </div>
            <Rank-title class="mt-[38px] mb-[-12px]"
                        :show-icon="false">年度CP榜</Rank-title>
            <div class="rich-tab-item is-cp"
                 @click="changeTab(cpTab.value)">
                <p class="title-text">{{cpTab.title}}</p>
            </div>
        </div>
        <Arrow-btn />
    </div>
</template>

<script>
export default {
    data() {
        return {
            currentTab: 'LiveMvp',
            richTab: {
                title: '神壕榜TOP10',
                value: 'Rich',
            },
            blackGoldTab: {
                title: '大人物榜TOP10',
                value: 'BlackGoldPerson',
                isLive: true,
            },
            cpTab: {
                title: 'CP榜TOP10',
                value: 'Cp',
                isCp: true,
            },
            // hotTab: {
            //     title: '人气榜TOP10',
            //     value: 'Hot',
            // },
        };
    },
    methods: {
        changeTab(value) {
            this.currentTab = value;
            this.toAppointPage(value);
        },
    },
};
</script>

<style lang="less" scoped>
.rich-tab-item {
    margin: 0 auto;
    padding-top: 26px;
    width: 238px;
    height: 73.5px;
    background-image: url('/static/img/<EMAIL>');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;

    &.is-live {
        background-image: url('/static/img/<EMAIL>');
    }
    &.is-cp {
        background-image: url('/static/img/<EMAIL>');
    }
    > .title-text {
        font-size: 17px;
        font-family: 'HYLingXinSquare';
        .text-gradient(linear-gradient(0deg,#451908 0%, #d7611a 100%));
        line-height: 23px;
        text-align: center;
    }
}
.rich-tab-item + .rich-tab-item {
    margin-top: 7px;
}
</style>
