import Vue from 'vue';
import 'tailwindcss/tailwind.css';

import { isInENV, loadScriptSync, loadRemoteFile } from './utils/utils';
import { getENV } from './config/url';
import myWebview, { parseUrlQuery } from './utils/webview-init'; // webview方法
import App from './App';
import store from './store';
import './utils/filters'; // 过滤器
import './utils/directive'; // 常用自定义指令
import mixins from './utils/mixins'; // 全局mixins

import './f7Components/index';

// 初始化生成判断是否需要端外分享
window.isShare = true;

// 生成当前马甲的key
let key;
const { market_id: marketId, uid, noSentry } = parseUrlQuery();
if (+marketId === 2) key = 'hy'; // 欢游
else if (+marketId === 5) key = 'mk'; // 麦可
else if (+marketId === 6) key = 'mj'; // 迷境
else key = 'tt';
// 全局当前环境判断
window.KEY = key;

Vue.mixin(mixins);
Vue.prototype.$eventBus = new Vue(); // 创建全局总线，挂在在vue实例上
window.myWebview = myWebview;

async function main() {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'setRightButtonList', JSON.stringify([3])); // 只保留右上角刷新
        } catch (e) {
            // eslint-disable-next-line no-alert
            // window.alert(e);
        }
    }
    // 测试&灰度环境提前加载vconsole
    // vconsole添加vuex的地址： https://cdn.jsdelivr.net/npm/vue-vconsole-devtools@1.0.5/dist/vue_plugin.js
    if (isInENV(['gray', 'testing', 'internal', 'dev']) && !parseUrlQuery().novconsole) {
        await loadScriptSync('https://ga-album-cdnqn.52tt.com/web/vconsole/vconsole3.9.1.min.js').then(() => {
            // eslint-disable-next-line no-undef
            const vConsole = new VConsole();
            // vconsole里添加vuex调试工具，记得先引入
            // const Devtools = window.vueVconsoleDevtools;
            // Devtools.initPlugin(vConsole);
            // 在Vue 2.x版本中，使用生产模式的vue.js包时，需要手动开启Dev模式
            // window.__VUE_DEVTOOLS_GLOBAL_HOOK__.emit('init', Vue);
            // Vue.config.devtools = true;
        });
    }
    if (parseUrlQuery().mock) {
        // 加载mockjs
        await loadScriptSync('https://ga-album-cdnqn.52tt.com/web/lib/mockjs/1.0.1-beta3/mock-min.js');
    }
    const root = new Vue({
        el: '#app',
        store,
        render: (h) => h(App),
    });
}

main();
