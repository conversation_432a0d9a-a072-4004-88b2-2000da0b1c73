import dayjs from 'dayjs';

const day26 = ['MusicTalent', 'EmotionTalent', 'TalkTalent', 'AnimeTalent'];
const day29 = ['Person1', 'Person2', 'Person3', 'Person4', 'Person5'];
dayjs.extend(require('dayjs/plugin/utc'));
dayjs.extend(require('dayjs/plugin/timezone'));
dayjs.tz.setDefault('Asia/Shanghai');

window.serverTime;

/* eslint-disable */
export default {
    // 当前项目名，涉及到地址的路径以及埋点，sentry追踪
    projectName: 'anniversary-guild-honor-2025',
    // 活动上下线操作，可重复操作
    __isOffline: '____ActivityOnlineTag____flag',
    // 后端api的项目路径
    nodePath: 'yule-znq-2025', // 娱乐 主path
    nodeRichPath: 'yule-ndsd-2025-rich', // cp 神壕 path
    yuleNodePath: 'yule-znq-2025', // 娱乐path
    liveNodePath: 'live-znq-2506',
    blackGoldNodePath: 'yingshou-black-gold-2025', // 黑金大人物 path
    liveTalentNodePath: 'live-talent-play-2025', // 听听才艺 path
    liveKnightNodePath: 'live-znq-play-2506', // 听听骑士榜 path
    liveHotNodePath: 'popularity-match-24', // 听听人气榜 path
    Person1NodePath: 'celebration-big-r-20250715', // 1 path
    Person2NodePath: 'yingshou-act-1-2025', // 2 path
    Person3NodePath: 'revenue-two-2025', // 3 path
    Person4NodePath: 'golden-legend-2507', // 4 path
    Person5NodePath: 'anniversary-four-2507', // 5 path

    pageSections: [
        'Open',
        'Guild',
        'QiangTing',
        'RecreationPerson',
        'LivePerson',
        'RecreationOther',
        'HotOther',
        'LiveTalent',
        'HonorGuild',
        'KingGuild',
        'LiveKingGuild',
        'DiamondGuild',
        'QiangTingFinal',
        'QiangTingMusic',
        'QiangTingGame',
        'QiangTing2D',
        'QiangTingInteraction',
        'RecreationMvp',
        'StarPerson',
        'FirePerson',
        'WindPerson',
        'SkyPerson',
        'LiveMvp',
        'MusicPerson',
        'EmotionPerson',
        'TalentPerson',
        'NewStarPerson',
        'OrePerson',
        'Rich',
        'BlackGoldPerson',
        'Cp',
        'Hot',
        'LiveHot',
        'Knight',
        'MusicTalent',
        'EmotionTalent',
        'TalkTalent',
        'AnimeTalent',
        'Person1',
        'Person2',
        'Person3',
        'Person4',
        'Person5',
        'YulePerson',
    ],
    visiblePages: [
        'Open',
        'Guild',
        // 'QiangTing',
        'RecreationPerson',
        'LivePerson',
        // 'RecreationOther',
        // 'HotOther',
        'LiveTalent',
        'OrePerson',
        'YulePerson',

        // 下面是排序
        'HonorGuild',
        'KingGuild',
        'LiveKingGuild',
        'DiamondGuild',
        // 'QiangTingFinal',
        // 'QiangTingMusic',
        // 'QiangTingGame',
        // 'QiangTing2D',
        // 'QiangTingInteraction',
        'RecreationMvp',
        'StarPerson',
        'FirePerson',
        'WindPerson',
        'SkyPerson',

        'LiveMvp',
        'MusicPerson',
        'EmotionPerson',
        'TalentPerson',
        'NewStarPerson',

        'Knight',
        'MusicTalent',
        'EmotionTalent',
        'TalkTalent',
        'AnimeTalent',

        'OreHong',
        'OreHuang',
        'OreBai',
        'OreLan',
        'OreLv',
        'OreZi',
        // 'Rich',
        // 'BlackGoldPerson',
        // 'Cp',
        // 'Hot',
        // 'LiveHot',
        'Person1',
        'Person2',
        'Person3',
        'Person4',
        'Person5',
    ],
    rankingText: {
        1: '冠军',
        2: '亚军',
        3: '季军',
        4: '第4名',
        5: '第5名',
        6: '第6名',
        7: '第7名',
        8: '第8名',
        9: '第9名',
        10: '第10名',
    },
};
