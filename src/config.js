/* eslint-disable */
export default {
    // 当前项目名，涉及到地址的路径以及埋点，sentry追踪
    projectName: 'anniversary-guild-honor-2025',
    // 活动上下线操作，可重复操作
    __isOffline: '____ActivityOnlineTag____flag',
    // 后端api的项目路径
    nodePath: 'yule-znq-2025', // 娱乐 主path
    nodeRichPath: 'yule-ndsd-2025-rich', // cp 神壕 path
    yuleNodePath: 'yule-znq-2025', // 娱乐path
    liveNodePath: 'live-ndsd-2025',
    blackGoldNodePath: 'yingshou-black-gold-2025', // 黑金大人物 path
    liveTalentNodePath: 'live-talent-2025', // 听听才艺 path
    liveKnightNodePath: 'live-ndsd-play-2025', // 听听骑士榜 path
    liveHotNodePath: 'popularity-match-24', // 听听人气榜 path
    pageSections: [
        'Open',
        'Guild',
        'QiangTing',
        'RecreationPerson',
        'LivePerson',
        'RecreationOther',
        'HotOther',
        'LiveTalent',
        'HonorGuild',
        'KingGuild',
        'LiveKingGuild',
        'DiamondGuild',
        'QiangTingFinal',
        'QiangTingMusic',
        'QiangTingGame',
        'QiangTing2D',
        'QiangTingInteraction',
        'RecreationMvp',
        'StarPerson',
        'FirePerson',
        'WindPerson',
        'SkyPerson',
        'LiveMvp',
        'MusicPerson',
        'EmotionPerson',
        'TalentPerson',
        'NewStarPerson',
        'Rich',
        'BlackGoldPerson',
        'Cp',
        'Hot',
        'LiveHot',
        'Knight',
        'MusicTalent',
        'EmotionTalent',
        'TalkTalent',
        'AnimeTalent',
    ],
    visiblePages: [
        'Open',
        'Guild',
        // 'QiangTing',
        'RecreationPerson',
        'LivePerson',
        'RecreationOther',
        'HotOther',
        'LiveTalent',
        'HonorGuild',
        'KingGuild',
        'LiveKingGuild',
        'DiamondGuild',
        // 'QiangTingFinal',
        // 'QiangTingMusic',
        // 'QiangTingGame',
        // 'QiangTing2D',
        // 'QiangTingInteraction',
        'RecreationMvp',
        'StarPerson',
        'FirePerson',
        'WindPerson',
        'SkyPerson',
        'LiveMvp',
        'MusicPerson',
        'EmotionPerson',
        'TalentPerson',
        'NewStarPerson',
        'Rich',
        'BlackGoldPerson',
        'Cp',
        'Hot',
        'LiveHot',
        'Knight',
        'MusicTalent',
        'EmotionTalent',
        'TalkTalent',
        'AnimeTalent',
    ],
    rankingText: {
        1: '冠军',
        2: '亚军',
        3: '季军',
        4: '第4名',
        5: '第5名',
        6: '第6名',
        7: '第7名',
        8: '第8名',
        9: '第9名',
        10: '第10名',
    },
};
