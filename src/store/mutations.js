export default {
    // html资源加载完毕
    HTML_LOADED(state, payload) {
        state.htmlLoaded = payload;
    },
    // f7挂载完毕
    F7_READY(state, payload) {
        state.f7Ready = payload;
    },
    // 通用赋值
    COMMON_SET_DATA(state, { key, value }) {
        state[key] = value;
    },
    RANK_LIST(state, { key, data }) {
        state[key] = data;
    },
    GUILD_RANK_LIST(state, { key, data }) {
        state[key] = data;
    },
    PERSON_RANK_LIST(state, { key, data }) {
        const keys = Object.keys(data);
        keys.forEach((type) => {
            state[key][type] = data[type]?.list || data[type];
        });
    },
    LIVE_GUILD_RANK_LIST(state, { key, data }) {
        const list = data || [];
        list.forEach((item) => {
            state[key][item.raceId] = item.list || [];
        });
    },
    TALENT_RANK_LIST(state, { key, data }) {
        const group = data || [];
        group.forEach((item) => {
            state[key][item.area] = item.list || [];
        });
    },
    LIVE_RANK_LIST(state, { key, data }) {
        const keys = data.map((item) => item.raceId);
        keys.forEach((type, i) => {
            const index = state[key].findIndex((item) => item.id === type);
            if (index >= 0) {
                state[key][index].list = data[i].list.map((item) => ({ ...item, ...item.guildInfo, ...item.userInfo }));
            }
        });
    },
    LIVE_ANCHOR_RANK_LIST(state, { key, data }) {
        const list = data.anchorList || [];
        state[key].mvp = data?.mvpList || [];
        list.forEach((item) => {
            state[key][item.raceId] = item.list || [];
        });
    },
    QIANGTING_RANK_LIST(state, { key, data }) {
        const list = data || [];
        ['final', 'music', 'game', '2d', 'interaction'].forEach((type, index) => {
            state[key][type] = list[index]?.list || [];
        });
    },
};
