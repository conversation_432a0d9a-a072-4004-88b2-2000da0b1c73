import dayjs from 'dayjs';

export default {
    htmlAllReady(state) {
        return state.htmlLoaded && state.f7Ready;
    },
    isRevenueBegin(state) {
        // 2025/12/29 00:00:00
        return state.serverTime >= dayjs('2025-12-29 00:00:00').unix(); // todo: 改回来
    },
    // isEnd(state) {
    //     const { serverTime, endTime } = state.initData;
    //     return serverTime >= endTime;
    // },
};
