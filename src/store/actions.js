import http, { createAxios } from '@/utils/http';
import { parseUrlQuery } from '@/utils/webview-init';
import {
    nodeLiveUrl,
    nodeBlackGoldUrl,
    nodeLiveTalentUrl,
    nodeUrl,
    nodeYuleUrl,
    nodeRichUrl,
    nodeLiveKnightUrl,
    nodeLiveHotUrl,
    nodePerson1Url,
    nodePerson2Url,
    nodePerson3Url,
    nodePerson4Url,
    nodePerson5Url,
} from '@/config/url';
import getMockData from './mock';

const request = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    const { proPrefix } = state.request;
    return http.post(proPrefix + api, {
        uid,
        ...data,
    });
};

// const liveRequest = ({ state, api, data = {} }) => {
//     const { uid } = parseUrlQuery();
//     if (parseUrlQuery().mock) {
//         return getMockData(api, data);
//     }
//     return createAxios({
//         baseURL: '',
//         baseNodeUrl: nodeLiveUrl(),
//     }).post(`/honor.Honor/${api}`, {
//         uid,
//         ...data,
//     });
// };

// 娱乐 服务请求
export const yuleRequest = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeUrl(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 娱乐 服务请求
export const yuleRequestWithoutMain = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeYuleUrl(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// cp 神壕 服务请求
export const richRequest = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeRichUrl(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 黑金大人物 服务请求
const blackGoldRequest = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeBlackGoldUrl(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 听听才艺 服务请求
export const liveTalentRequest = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeLiveTalentUrl(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 听听 服务请求
export const liveRequest = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeLiveUrl(),
    }).post(`/honor.Honor/${api}`, {
        uid,
        ...data,
    });
};

// 听听骑士榜 服务
export const liveKnightRequest = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeLiveKnightUrl(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 听听人气榜 服务请求
export const liveHotRequest = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodeLiveHotUrl(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 1 服务请求
export const person1Request = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodePerson1Url(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 2 服务请求
export const person2Request = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodePerson2Url(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 3 服务请求
export const person3Request = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodePerson3Url(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 4 服务请求
export const person4Request = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodePerson4Url(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 5 服务请求
export const person5Request = ({ state, api, data = {} }) => {
    const { uid } = parseUrlQuery();
    if (parseUrlQuery().mock) {
        return getMockData(api, data);
    }
    return createAxios({
        baseURL: '',
        baseNodeUrl: nodePerson5Url(),
    }).post(`/activity.Activity/${api}`, {
        uid,
        ...data,
    });
};

// 模拟服务器时间
function countServerTime(state, serverTime) {
    if (state.countTimeKey) {
        clearInterval(state.countTimeKey);
        state.countTimeKey = null;
    }
    window.serverTime = serverTime;
    state.serverTime = serverTime;
    state.countTimeKey = setInterval(() => {
        window.serverTime += 1;
        state.serverTime += 1;
    }, 1000);
}

// =====================接口请求=====================

// 数据初始化
export const init = async ({ state, commit }, payload) => {
    const { init } = state.request;
    try {
        const { code, data } = await yuleRequest({
            state,
            api: 'init',
            data: payload,
        });
        commit('COMMON_SET_DATA', { key: 'initData', value: data });
        countServerTime(state, data.serverTime);
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新黑金大人物排行榜 总榜top10
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updateBlackGoldRank = async ({ state, commit }) => {
    try {
        const { code, data } = await blackGoldRequest({
            state,
            api: 'rankList',
            data: {
                page: 1,
                size: 10,
            },
        });
        commit('RANK_LIST', { key: 'blackGoldRank', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新听听才艺排行榜
 * @param {*} state
 * @param {*} commit
 * @param {*} rankArea 赛区 1:音乐 2:情感 3:二次元 4:脱口秀
 * @returns
 */
export const updateLiveTalentRank = async ({ state, commit }, rankArea = 0) => {
    try {
        const { code, data } = await liveTalentRequest({
            state,
            api: 'honorListGroup',
        });
        commit('TALENT_RANK_LIST', { key: 'liveTalentRank', data: data?.group || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新娱乐 公会榜
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updateGuildRank = async ({ state, commit }) => {
    try {
        const { code, data } = await yuleRequest({
            state,
            api: 'gloryGuildRanking',
        });
        commit('RANK_LIST', { key: 'guildRank', data });
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新娱乐 个人榜
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updatePersonalRank = async ({ state, commit }) => {
    try {
        const { code, data } = await yuleRequest({
            state,
            api: 'gloryPersonalRanking',
        });
        commit('PERSON_RANK_LIST', { key: 'personRank', data });
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新cp 神壕榜
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updateCpRank = async ({ state, commit }) => {
    try {
        const { code, data } = await richRequest({
            state,
            api: 'gloryCpRanking',
        });
        commit('RANK_LIST', { key: 'cpRank', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新神壕榜
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updateRichRank = async ({ state, commit }) => {
    try {
        const { code, data } = await richRequest({
            state,
            api: 'gloryRichRanking',
        });
        commit('RANK_LIST', { key: 'richRank', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新娱乐 人气榜
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updateHotRank = async ({ state, commit }) => {
    try {
        const { code, data } = await yuleRequest({
            state,
            api: 'gloryHotRanking',
        });
        commit('RANK_LIST', { key: 'hotRank', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};
// 更新娱乐晶石榜
export const updateOreRank = async ({ state, commit }) => {
    try {
        // 定义需要请求的区域
        const areas = ['huang', 'bai', 'zi', 'hong', 'lv', 'lan'];

        const result = {
            huang: [],
            bai: [],
            zi: [],
            hong: [],
            lv: [],
            lan: [],
        };
        // 并行请求每个区域的数据
        const areaResults = await Promise.all(
            areas.map(
                (area) =>
                    yuleRequest({
                        api: 'oreRank',
                        data: {
                            page: 1,
                            size: 3,
                            area,
                        },
                    }),
                // eslint-disable-next-line function-paren-newline
            ),
        );

        areas.forEach((area, idx) => {
            result[area] = areaResults[idx]?.data?.list || [];
        });

        commit('RANK_LIST', { key: 'oreRank', data: result });
        return { code: 0 };
    } catch (e) {
        return false;
    }
};

/**
 * 更新听听 公会榜
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updateLiveGuildRank = async ({ state, commit }) => {
    try {
        const { code, data } = await liveRequest({
            state,
            api: 'guildHonor',
        });
        commit('LIVE_GUILD_RANK_LIST', { key: 'liveGuildRank', data: data?.guildList || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

export const updateLiveAnchorRank = async ({ state, commit }) => {
    try {
        const { code, data } = await liveRequest({
            state,
            api: 'anchorHonor',
        });
        commit('LIVE_ANCHOR_RANK_LIST', { key: 'liveAnchorRank', data: data || {} });
        return { code };
    } catch (e) {
        return false;
    }
};

/**
 * 更新听听骑士榜
 * @param {*} state
 * @param {*} commit
 * @returns
 */
export const updateLiveKnightRank = async ({ state, commit }) => {
    try {
        const { code, data } = await liveKnightRequest({ state, api: 'getRank', data: { page: 1, size: 10 } });
        commit('RANK_LIST', { key: 'liveKnightRank', data: data?.list || {} });
        return { code };
    } catch (e) {
        return false;
    }
};

export const updateLiveHotRank = async ({ state, commit }) => {
    try {
        const { code, data } = await liveHotRequest({ state, api: 'getTopListTotal', data: {} });
        commit('RANK_LIST', { key: 'liveHotRank', data: data?.listInfo || {} });
        return { code };
    } catch (e) {
        return false;
    }
};

export const updateQiangTingRank = async ({ state, commit }) => {
    try {
        const { code, data } = await yuleRequestWithoutMain({ state, api: 'channelRankGroup', data: {} });
        commit('QIANGTING_RANK_LIST', { key: 'qiangTingRank', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

export const commonSetData = async ({ state, commit }, { key, value }) => {
    commit('COMMON_SET_DATA', { key, value });
};

// 娱乐各活动

export const updatePerson1Rank = async ({ state, commit }) => {
    try {
        const { code, data } = await person1Request({
            state,
            api: 'getSupremeRank',
            data: {
                page: 1,
                size: 10,
            },
        });
        commit('RANK_LIST', { key: 'person1', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

export const updatePerson2Rank = async ({ state, commit }) => {
    try {
        const { code, data } = await person2Request({
            state,
            api: 'rankList',
            data: {
                type: 1,
                page: 1,
                size: 10,
            },
        });
        commit('RANK_LIST', { key: 'person2', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

export const updatePerson3Rank = async ({ state, commit }) => {
    try {
        const { code, data } = await person3Request({
            state,
            api: 'getUserRanking',
            data: {
                type: 1,
                page: 1,
                pageSize: 10,
            },
        });
        commit('RANK_LIST', { key: 'person3', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

export const updatePerson4Rank = async ({ state, commit }) => {
    try {
        const { code, data } = await person4Request({
            state,
            api: 'giftRank',
            data: {
                type: 1,
                page: 1,
                size: 10,
            },
        });
        commit('RANK_LIST', { key: 'person4', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};

export const updatePerson5Rank = async ({ state, commit }) => {
    try {
        const { code, data } = await person5Request({
            state,
            api: 'getRank',
            data: {
                type: 1,
                page: 1,
                size: 10,
            },
        });
        commit('RANK_LIST', { key: 'person5', data: data?.list || [] });
        return { code };
    } catch (e) {
        return false;
    }
};
