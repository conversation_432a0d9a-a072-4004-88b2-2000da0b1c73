export default {
    request: {
        proPrefix: '/activity.Activity/', // 接口前缀
        init: 'init',
        gloryGuildRanking: 'gloryGuildRanking', // 娱乐公会榜
        guildHonor: 'guildHonor', // 语音直播公会榜
        gloryPersonalRanking: 'gloryPersonalRanking', // 娱乐个人榜
        anchorHonor: 'anchorHonor', // 主播荣誉榜单
        gloryRichRanking: 'gloryRichRanking', // 神壕榜
        gloryCpRanking: 'gloryCpRanking', // cp榜
        gloryHotRanking: 'gloryHotRanking', // 人气榜
    },
    /** 页面基础信息 */
    htmlLoaded: false, // 页面资源加载完毕, js/css/resource
    f7Ready: false, // f7挂载完毕
    /** 业务数据 */
    initData: {},
    serverTime: 0,
    guildRank: {
        legend: [], // 传奇
        king: [], // 王者
        self: {},
    },
    LiveGuildRank: [
        {
            id: 'great',
            list: [],
        },
        {
            id: 'honor',
            list: [],
        },
    ],
    personRank: {
        mvp: [], // mvp
        star: [],
        fire: [],
        wind: [],
        sky: [],
    },
    LivePersonRank: [
        {
            id: 'music',
            list: [],
        },
        {
            id: 'emotion',
            list: [],
        },
        {
            id: 'talent',
            list: [],
        },
        {
            id: 'newstar',
            list: [],
        },
    ],
    LivePersonMvp: {},
    richRank: [], // 神壕榜
    cpRank: [], // 娱乐 cp榜
    hotRank: [], // 娱乐 人气榜
    blackGoldRank: [], // 黑金
    topBarHeight: 0,
    liveGuildRank: {
        great: [], // 超凡
        honor: [], // 荣耀
    }, // 听听 公会榜
    // 听听才艺排行榜 赛区 1:音乐 2:情感 3:二次元 4:脱口秀
    liveTalentRank: {
        1: [], // 音乐
        2: [], // 情感
        3: [], // 二次元
        4: [], // 脱口秀
    },
    liveAnchorRank: {
        music: [], // 音乐
        emotion: [], // 情感
        newstar: [], // 新星
        talent: [], // 才艺
        mvp: [], // mvp
    }, // 听听 达人榜
    liveKnightRank: [], // 听听骑士榜
    liveHotRank: [], // 听听人气榜
    qiangTingRank: {
        final: [], // 决赛
        music: [], // 音乐
        game: [], // 游戏
        '2d': [], // 2次元
        interaction: [], // 互动
    },
};
