const utils = {
    numArr: (num = 100) =>
        Array(num)
            .fill(0)
            .map((one, index) => index),
};
const f = {
    getUserInfo(index = 0) {
        return {
            uid: 2199715,
            nickname: `Mock-我的昵称真的很长吗${index}`,
            headMd5: 'ver-**************',
            username: 'tt*********',
            account: 'tt*********',
            sex: window.Mock.Random.integer(0, 1),
            alias: '*********',
            ttid: '*********',
            roomId: 4324233,
            roomStatus: window.Mock.Random.integer(0, 4),
        };
    },
    getRankList(langth = 10, page = 1) {
        return utils.numArr(langth).map((one, index) => ({
            ...this.getUserInfo(page * langth - langth + index),
            rank: page * langth - langth + index + 1,
            preGapText: '距上一名20',
            preGapScore: 40,
            nextGapText: '超下一名20',
            nextGapScore: 10,
        }));
    },
};
const API = {
    init() {
        return {
            serverTime: new Date('2025/12/29 18:00:00').getTime() / 1000, // 服务器当前时间
            startTime: new Date('2025/07/15 18:00:00').getTime() / 1000, // 活动开始时间
            endTime: new Date('2025/07/29 00:30:00').getTime() / 1000, // 活动结束时
            userInfo: f.getUserInfo(),
        };
    },
    rankList(payload = {}) {
        return {
            rankList: f.getRankList(payload.size, payload.page),
            total: 100,
        };
    },
    myRank() {
        return f.getRankList(1)[0];
    },
};
const getMockData = (type, payload) => {
    const delay = Math.round(Math.random() * 10) * 50; // 模拟访问延迟
    // eslint-disable-next-line no-console
    console.warn(`[MOCKDATA] ${type} delay: ${delay} ms; payload: ${JSON.stringify(payload)}`);
    window.setTimeout(async () => {
        await Promise.resolve();
    }, delay);
    let data;
    if (typeof API[type] === 'function') {
        data = API[type](payload);
    } else {
        data = API[type];
    }
    return {
        code: 0,
        data,
        msg: '',
    };
};
export default getMockData;
