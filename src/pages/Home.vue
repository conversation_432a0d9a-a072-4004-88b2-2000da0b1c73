<!-- eslint-disable import/no-unresolved -->
<template>
    <div id="home">
        <PageTopBar
            :right-inner="rightHtml"
            :right-fn="handleShare"
            v-if="showStatusBar"
            ref="pageTopBar"
            class="absolute m-auto left-0 top-0 z-[99999999]"
            color="#1E1F21"
        />
        <div class="w-[100%] h-[100%] overflow-hidden">
            <div id="fullPage">
                <div
                    class="section"
                    v-for="(section, index) in sections"
                    :key="`section-${index}`"
                >
                    <component
                        :is="section"
                        ref="sectionComponent"
                    ></component>
                </div>
            </div>
        </div>
        <Share-sheet />
    </div>
</template>

<script>
import Vue from 'vue';
import { f7ready } from 'framework7-vue/utils/plugin';
import ActiveApp from '@tt/active-app';
import { loadScriptSync } from '@/utils/utils';
import { wxSDKInit } from '@/utils/wx-auth';
import config from '@/config';
import { activeAppOptions, jsBridgePreview } from '@/config/url';

import Open from '../components/other/Open'; // 首页
import Guild from '../components/guild/Guild'; // 公会榜
import RecreationPerson from '../components/person/RecreationPerson'; // 娱乐个人榜
import LivePerson from '../components/person/LivePerson'; // 直播个人榜
import RecreationOther from '../components/other/RecreationOther'; // 神壕/cp/人气榜
import HonorGuild from '../components/guild/HonorGuild'; // 娱乐荣耀王者公会
import KingGuild from '../components/guild/KingGuild'; // 娱乐王者公会
import LiveKingGuild from '../components/guild/LiveKingGuild'; // 直播王者公会
import DiamondGuild from '../components/guild/DiamondGuild'; // 直播钻石公会
import RecreationMvp from '../components/person/RecreationMvp'; // 娱乐MVP
import FirePerson from '../components/person/FirePerson'; // 娱乐火榜
import WindPerson from '../components/person/WindPerson'; // 娱乐风榜
import SkyPerson from '../components/person/SkyPerson'; // 娱乐天榜
import StarPerson from '../components/person/StarPerson'; // 娱乐星光
import LiveMvp from '../components/person/LiveMvp'; // 直播mvp
import MusicPerson from '../components/person/MusicPerson'; // 直播音乐榜
import EmotionPerson from '../components/person/EmotionPerson'; // 直播情感榜
import TalentPerson from '../components/person/TalentPerson'; // 直播才艺榜
import NewStarPerson from '../components/person/NewStarPerson'; // 直播新星榜
import Rich from '../components/other/Rich'; // 年度神壕榜
import Cp from '../components/other/Cp'; // Cp榜
import Hot from '../components/other/Hot'; // 人气榜
import HotOther from '../components/other/HotOther'; // 人气榜目录
import BlackGoldPerson from '../components/person/BlackGoldPerson'; // 黑金大人物榜
import LiveTalent from '../components/person/LiveTalent'; // 听听才艺榜
import MusicTalent from '../components/person/MusicTalent'; // 音乐赛道
import EmotionTalent from '../components/person/EmotionTalent'; // 情感赛道
import TalkTalent from '../components/person/TalkTalent'; // 脱口秀赛道
import AnimeTalent from '../components/person/AnimeTalent'; // 二次元赛道
import LiveHot from '../components/other/LiveHot';
import Knight from '../components/other/Knight'; // 听听骑士榜
import QiangTing from '../components/other/QiangTing'; // 强厅赛
import QiangTingGame from '../components/other/QiangTingGame'; // 强厅赛游戏榜
import QiangTingInteraction from '../components/other/QiangTingInteraction'; // 强厅赛互动榜
import QiangTingMusic from '../components/other/QiangTingMusic'; // 强厅赛音乐榜
import QiangTing2D from '../components/other/QiangTing2D'; // 强厅赛二次元榜
import QiangTingFinal from '../components/other/QiangTingFinal'; // 强厅赛全站榜
// 听听人气榜
export default {
    name: 'home',
    components: {
        Open,
        Guild,
        RecreationPerson,
        LivePerson,
        RecreationOther,
        HonorGuild,
        KingGuild,
        LiveKingGuild,
        DiamondGuild,
        RecreationMvp,
        FirePerson,
        WindPerson,
        SkyPerson,
        StarPerson,
        LiveMvp,
        MusicPerson,
        EmotionPerson,
        TalentPerson,
        NewStarPerson,
        Rich,
        Cp,
        Hot,
        HotOther,
        BlackGoldPerson,
        LiveTalent,
        MusicTalent,
        EmotionTalent,
        TalkTalent,
        AnimeTalent,
        LiveHot,
        Knight,
        QiangTing,
        QiangTingGame,
        QiangTingInteraction,
        QiangTingMusic,
        QiangTing2D,
        QiangTingFinal,
    },
    data() {
        return {
            rightHtml: `<img class="btn" style="width: 28px; height: 28px;" src="${require('/static/img/<EMAIL>')}" alt="share"></img>`,
            sections: [],
        };
    },
    async mounted() {
        f7ready(async () => {
            await this.init();
            const { pageTopBar } = this.$refs;
            if (pageTopBar && pageTopBar.$el) {
                this.$store.dispatch('commonSetData', {
                    key: 'topBarHeight',
                    value: pageTopBar.$el.clientHeight,
                });
            }
            this.setShareInfo();
        });
    },
    methods: {
        async init() {
            await this.$store.dispatch('init');
            if (!this.isRevenueBegin) {
                this.sections = config.visiblePages;
            } else {
                this.sections = config.pageSections;
            }
            await this.$nextTick();
            // this.isProxySupport();
            if (!this.$fp) {
                // 注册全局变量
                // eslint-disable-next-line new-cap
                Vue.prototype.$fp = new window.fullpage('#fullPage', {
                    licenseKey: 'R1BMVjMtTElDRU5TRQ',
                    lockAnchors: true,
                    anchors: this.sections,
                    scrollHorizontally: true,
                    scrollHorizontallyKey: 'YWx2YXJvdHJpZ28uY29tX01mU2MyTnliMnhzU0c5eWFYcHZiblJoYkd4NVNRcg==',
                    loopHorizontal: false,
                    scrollingSpeed: 600,
                    onSlideLeave: this.onSlideLeave,
                    normalScrollElements: '.rank-list-box',
                });
            }
            this.$store.dispatch('updateHotRank');
            this.$store.dispatch('updateRichRank');
            this.$store.dispatch('updateCpRank');
            this.$store.dispatch('updatePersonalRank');
            this.$store.dispatch('updateGuildRank');
            this.$store.dispatch('updateBlackGoldRank');
            this.$store.dispatch('updateLiveTalentRank');
            this.$store.dispatch('updateLiveGuildRank');
            this.$store.dispatch('updateLiveAnchorRank');
            this.$store.dispatch('updateLiveKnightRank');
            this.$store.dispatch('updateLiveHotRank');
            // this.$store.dispatch('updateQiangTingRank');
        },
        onSlideLeave(section, origin, destination, direction, trigger) {
            // console.log(section, origin, destination, direction, trigger);
            const { sectionComponent } = this.$refs;
            if (section.anchor === 'Cp') {
                sectionComponent[section.index].changeSlideStatus(destination.index);
            } else if (sectionComponent[section.index]) {
                const { rankDetailsContent } = sectionComponent[section.index].$refs;
                if (rankDetailsContent) {
                    rankDetailsContent.changeSlideStatus(destination.index);
                }
            }
        },
        // 全局分享
        handleShare() {
            this.openModal('#share-sheet', 'sheet');
        },
        setShareInfo() {
            if (window.myWebview.isInApp()) {
                return;
            }
            if (window.myWebview.isInWx()) {
                loadScriptSync('https://ga-album-cdnqn.52tt.com/web/lib/jweixin-1.6.0.js').then(async () => {
                    await wxSDKInit(['updateAppMessageShareData']);
                    window.wx.updateAppMessageShareData({
                        title: '2025年度盛典赛事名人堂', // 分享标题
                        desc: '年度盛典群雄荟萃，谁是无冕之王', // 分享描述
                        link: window.location.href, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
                        imgUrl: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20251224110314_92468145.png', // 分享图标
                        success() {
                            // eslint-disable-next-line no-console
                            console.log('分享信息设置成功');
                        },
                    });
                });
            } else if (window.myWebview.isInQq()) {
                loadScriptSync('https://ga-album-cdnqn.52tt.com/web/lib/qqapi.js').then(
                    () => {
                        window.mqq.data.setShareInfo({
                            title: '2025年度盛典赛事名人堂',
                            desc: '年度盛典群雄荟萃，谁是无冕之王',
                            image_url: 'https://obs-cdn.52tt.com/tt/fe-moss/web/20251224110314_92468145.png',
                            share_url: window.location.href,
                        });
                    },
                    (e) => {
                        // console.log(e);
                    },
                );
            }
            const options = {
                ...activeAppOptions,
                action: `${jsBridgePreview}/web?url=${window.encodeURIComponent(
                    `${window.location.protocol}//${window.location.host}${window.location.pathname}?immersion=1`,
                )}`,
            };
            console.log('options', options);
            window.activeApp = new ActiveApp(options);
        },
    },
};
</script>

<style lang="less" scoped>
#home {
    position: relative;
    width: 100vw;
    height: 100vh;
}

.section {
    width: 100vw;
    height: 100vh;
}
</style>
