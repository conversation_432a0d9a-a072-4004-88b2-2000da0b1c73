import Vue from 'vue';

// 引入全局f7基础样式
// import 'framework7/css/framework7.min.css';

/* 按需引入，以及对应样式
 * 可根据node_modules里的模块查找需要引入的模块以及对应的样式
 */

// 基础f7库
import Framework7 from 'framework7/framework7-lite.esm';

// dialog
import Dialog from 'framework7/components/dialog/dialog';
import 'framework7/components/dialog.css';
// Sheet
import Sheet from 'framework7/components/sheet/sheet';
import 'framework7/components/sheet.css';
// preloader
import Preloader from 'framework7/components/preloader/preloader';
import 'framework7/components/preloader.css';
// toast
import Toast from 'framework7/components/toast/toast';
import 'framework7/components/toast.css';
// popup
// import Popup from 'framework7/components/popup/popup';
// import 'framework7/components/popup.css';

// 滚动触发
// import InfiniteScroll from 'framework7/components/infinite-scroll/infinite-scroll';
// import 'framework7/components/infinite-scroll.css';

// import Framework7Vue, { f7Badge, f7App, f7Page, f7PageContent, f7View } from 'framework7-vue';
// 按需引入
import Framework7Vue from 'framework7-vue/utils/plugin';
import f7Badge from 'framework7-vue/components/badge';
import f7App from 'framework7-vue/components/app';
import f7Page from 'framework7-vue/components/page';
import f7PageContent from 'framework7-vue/components/page-content';
import f7View from 'framework7-vue/components/view';

// 挂载在f7的方法
Framework7.use(Framework7Vue);
Framework7.use(Dialog);
Framework7.use(Sheet);
Framework7.use(Preloader);
Framework7.use(Toast);
// Framework7.use(Popup);
// Framework7.use(InfiniteScroll);

// 按需引入组件
Vue.component('f7-badge', f7Badge);
Vue.component('f7-app', f7App);
Vue.component('f7-page', f7Page);
Vue.component('f7-page-content', f7PageContent);
Vue.component('f7-view', f7View);
