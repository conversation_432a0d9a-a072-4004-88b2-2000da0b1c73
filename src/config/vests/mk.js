/**
 * @description 目前有tt(tt语音),hy(欢游),mk(麦可)3个马甲包配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * */

export default {
    api_ios: {
        prod: '//api.tingyou.fun/',
        gray: '//api.tingyou.fun/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    api_android: {
        prod: '//api.yuyue27.com/',
        gray: '//api.yuyue27.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    node_ios: {
        prod: '//node-hw.tingyou.fun/activity-production/',
        gray: '//node-hw.tingyou.fun/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_android: {
        prod: '//node-hw.yuyue27.com/activity-production/',
        gray: '//node-hw.yuyue27.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    avatar_ios: {
        prod: 'https://avatar.tingyou.fun/v2/',
        gray: 'https://avatar.tingyou.fun/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://avatar.yuyue27.com/v2/',
        gray: 'https://avatar.yuyue27.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.tingyou.fun/gray/frontend-web-activity-',
        prod: 'https://appcdn.tingyou.fun/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.tingyou.fun/gray/frontend-web-activity-',
        prod: 'https://cdn.tingyou.fun/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.yuyue27.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.yuyue27.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.yuyue27.com/gray/frontend-web-activity-',
        prod: 'https://cdn.yuyue27.com/web/frontend-web-activity-',
    },
    activeAppOptions: {
        androidLink: 'm://yuyue27.com/',
        universal: 'https://ul.tingyou.fun',
        appstore: 'https://itunes.apple.com/cn/app/id1619035018',
        fallback: 'http://a.app.qq.com/o/simple.jsp?pkgname=com.yuyue.mic',
        official: 'https://d.yuyue27.com/maike/official/maike.apk',
        marketId: 5,
    },
    jsBridgeProtocol: 'ymic://',
    jsBridgePreview: 'ymic://m.tingyou.fun',
    website_ios: 'https://web.tingyou.fun',
    website_android: 'https://yuyue27.com',
};
