/**
 * @description 秘境配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * */

export default {
    api_ios: {
        prod: '//api.ukilive.com/',
        gray: '//api.ukilive.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    api_android: {
        prod: '//api.ukilive.com/',
        gray: '//api.ukilive.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    node_ios: {
        prod: '//node-hw.ukilive.com/activity-production/',
        gray: '//node-hw.ukilive.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_android: {
        prod: '//node-hw.ukilive.com/activity-production/',
        gray: '//node-hw.ukilive.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    avatar_ios: {
        prod: 'https://avatar.ukilive.com/v2/',
        gray: 'https://avatar.ukilive.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://avatar.ukilive.com/v2/',
        gray: 'https://avatar.ukilive.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://gray.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.ukilive.com/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://gray.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://cdn.ukilive.com/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://gray.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.ukilive.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://gray.ukilive.com/gray/frontend-web-activity-',
        prod: 'https://cdn.ukilive.com/web/frontend-web-activity-',
    },
    activeAppOptions: {
        androidLink: 'm://ukilive.com',
        universal: 'https://ul.ukilive.com',
        appstore: 'https://apps.apple.com/mx/app/id1641884007',
        fallback: 'https://a.app.qq.com/o/simple.jsp?pkgname=com.ukilive.wonderland',
        official: 'https://d.ukilive.com/mijing/offcial/mijing.apk',
        marketId: 6,
    },
    jsBridgeProtocol: 'tmj://',
    jsBridgePreview: 'tmj://m.ukilive.com',
    website_ios: 'https://ukilive.com',
    website_android: 'https://ukilive.com',
};
