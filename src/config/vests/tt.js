/**
 * @description 目前有tt(tt语音),hy(欢游),mk(麦可)3个马甲包配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * */
export default {
    api_ios: {
        prod: '//api.52tt.com/',
        gray: '//api.52tt.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '',
    },
    api_android: {
        prod: '//api.52tt.com/',
        gray: '//api.52tt.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '',
    },
    node_ios: {
        prod: '//node-hw.52tt.com/activity-production/',
        gray: '//node-hw.52tt.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '',
    },
    node_android: {
        prod: '//node-hw.52tt.com/activity-production/',
        gray: '//node-hw.52tt.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '',
    },
    avatar_ios: {
        prod: 'https://avatar.52tt.com/v2/',
        gray: 'https://avatar.52tt.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://avatar.52tt.com/v2/',
        gray: 'https://avatar.52tt.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'http://app.52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.52tt.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.52tt.com/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'http://app.i52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.i52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.i52tt.com/gray/frontend-web-activity-',
        prod: 'https://cdn.i52tt.com/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'http://app.52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.52tt.com/gray/frontend-web-activity-',
        prod: 'https://appcdn.52tt.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'http://app.i52tt.com/internal/frontend-web-activity-',
        internal: 'https://app.i52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.i52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.i52tt.com/gray/frontend-web-activity-',
        prod: 'https://cdn.i52tt.com/web/frontend-web-activity-',
    },
    activeAppOptions: {
        androidLink: 'm://52tt.com/', // Android的schemeUrl
        universal: 'https://ul.52tt.com', // ios的universalLink
        appstore: 'https://itunes.apple.com/cn/app/id1012317813', // ios appstore地址
        fallback: 'https://a.app.qq.com/o/simple.jsp?pkgname=com.yiyou.ga', // 应用宝
        official: 'https://d.52tt.com/tt/official/tt.apk',
        marketId: 0,
    },
    jsBridgeProtocol: 'tt://',
    jsBridgePreview: 'tt://m.52tt.com',
    website_ios: 'https://52tt.com',
    website_android: 'https://52tt.com',
};
