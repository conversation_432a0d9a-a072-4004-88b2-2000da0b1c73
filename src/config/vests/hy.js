/**
 * @description 目前有tt(tt语音),hy(欢游),mk(麦可)3个马甲包配置
 * @description ios，android表示当前系统，tt其实是统一，但是hy跟mk需要
 * @param api: 后台服务地址
 * @param node: 活动的node服务地址
 * @param web: 活动页面地址
 * @param activeAppOptions: 唤醒包的配置表
 * @param jsBridgeProtocol: 跟客户端对接的JSBridge协议头
 * @param jsBridgePreview: 跟客户端对接的JSBridge协议头+域名
 * @param website: 对应当前马甲包的官网
 * */

export default {
    api_ios: {
        prod: '//api.tses.net/',
        gray: '//api.tses.net/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    api_android: {
        prod: '//zy-api.rzhushou.com/',
        gray: '//zy-api.rzhushou.com/',
        testing: '//testing-tt-web-tc.ttyuyin.com/',
        dev: '//testing-tt-web-tc.ttyuyin.com/',
    },
    node_ios: {
        prod: '//node-hw.tses.net/activity-production/',
        gray: '//node-hw.tses.net/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    node_android: {
        prod: '//zy-node-hw.rzhushou.com/activity-production/',
        gray: '//zy-node-hw.rzhushou.com/activity-testing/',
        testing: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
        dev: '//testing-tt-web-tc.ttyuyin.com/activity-testing/',
    },
    avatar_ios: {
        prod: 'https://avatar.tses.net/v2/',
        gray: 'https://avatar.tses.net/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    avatar_android: {
        prod: 'https://zy-avatar.rzhushou.com/v2/',
        gray: 'https://zy-avatar.rzhushou.com/v2/',
        testing: 'https://testing-avatar.ttyuyin.com/v2/',
        dev: 'https://testing-avatar.ttyuyin.com/v2/',
    },
    web_ios: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.tses.net/gray/frontend-web-activity-',
        prod: 'https://appcdn.tses.net/web/frontend-web-activity-',
    },
    web_ios_share: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://app.tses.net/gray/frontend-web-activity-',
        prod: 'https://cdn.tses.net/web/frontend-web-activity-',
    },
    web_android: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://zy-app.rzhushou.com/gray/frontend-web-activity-',
        prod: 'https://zy-appcdn.rzhushou.com/web/frontend-web-activity-',
    },
    web_android_share: {
        dev: 'https://app.52tt.com/testing/frontend-web-activity-',
        internal: 'https://app.52tt.com/internal/frontend-web-activity-',
        testing: 'https://app.52tt.com/testing/frontend-web-activity-',
        gray: 'https://zy-app.rzhushou.com/gray/frontend-web-activity-',
        prod: 'https://zy-cdn.rzhushou.com/web/frontend-web-activity-',
    },
    activeAppOptions: {
        androidLink: 'm://tses.net', // Android的schemeUrl
        universal: 'https://ul.tses.net', // ios的universalLink
        appstore: 'https://apps.apple.com/cn/app/id1612257488', // ios appstore地址
        fallback: 'https://a.app.qq.com/o/simple.jsp?pkgname=com.sabac.hy', // 应用宝
        official: 'https://zy-cdntt.rzhushou.com/packages/HuanYou/Mi4wLjBAMjY5NQ/official/huanyou.apk', // Android的apk包
        marketId: 2,
    },
    jsBridgeProtocol: 'tyf://',
    jsBridgePreview: 'tyf://m.tses.net',
    website_ios: 'https://zy.tses.net',
    website_android: 'https://zy.rzhushou.com',
};
