import { parseUrlQuery } from '@/utils/webview-init';
import tt from './vests/tt';
import hy from './vests/hy';
import mk from './vests/mk';
import mj from './vests/mj';

// 导出当前马甲的配置信息
const getCurrConfig = function () {
    const { market_id: marketId } = parseUrlQuery();
    if (+marketId === 2) return hy;
    else if (+marketId === 5) return mk;
    else if (+marketId === 6) return mj;
    return tt;
};

export const currConfig = getCurrConfig();
