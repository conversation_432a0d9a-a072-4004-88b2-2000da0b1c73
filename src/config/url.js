import myWebview, { parseUrlQuery } from '@/utils/webview-init';
import config from '@/config';
import { currConfig } from './urlConfig';

/**
 * @name getENV 根据URL获取当前环境
 * @returns {string} 返回当前环境 ['prod', 'gray', 'testing', 'internal', 'dev']
 */
export const getENV = () => {
    const urlString = window.location.href;
    const prod = ['.com/web/', '.net/web/', '.com/project/', '.net/project/', '.fun/web/'];
    const gray = ['.com/gray/', '.net/gray/', '.fun/gray/'];
    const testing = ['.com/testing/', '.net/testing/', '.fun/testing/'];
    const internal = ['.com/internal/', '.net/internal/'];
    if (checkIsMixed(prod, urlString)) {
        return 'prod';
    } else if (checkIsMixed(gray, urlString)) {
        return 'gray';
    } else if (checkIsMixed(testing, urlString)) {
        return 'testing';
    } else if (checkIsMixed(internal, urlString)) {
        return 'internal';
    } else {
        return 'dev';
    }
    function checkIsMixed(a, b) {
        return a.some(function (v) {
            return b.includes(v);
        });
    }
};

const env = getENV();
// const env = 'gray';
const osType = myWebview.isIOS() ? 'ios' : 'android';
const webKey = (_isShare = window.isShare) => {
    return _isShare ? `web_${osType}_share` : `web_${osType}`;
};

/**
 * @param env 环境
 * @param marketId 马甲类型 2欢游 5麦可 1TT语音
 * @param key 马甲类型  hy, mk, tt
 * @param osType 系统， 安卓或者IOS
 * @param urlConfig 配置表
 */

// 导出后端域名
export const apiUrl = function () {
    return `${currConfig[`api_${osType}`][env]}`;
};

// 导出node服务域名
export const nodeUrl = function () {
    const { localNode, shareMock, serverId } = parseUrlQuery();
    // 指向共享平台的mock服务
    if (shareMock) return `https://node-hw.52tt.com/app-production/fe-share-manager/mock/${config.nodePath}`;
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    // return `${currConfig[`node_${osType}`].gray}${config.nodePath}`;
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.nodePath}`;
    return `${currConfig[`node_${osType}`][env]}${serverId || config.nodePath}`;
};

// 导出娱乐node服务域名
export const nodeYuleUrl = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    // return `${currConfig[`node_${osType}`].prod}${config.yuleNodePath}`;
    if (env === 'gray') return `${currConfig[`node_${osType}`].prod}${config.yuleNodePath}`;
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.yuleNodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.yuleNodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.yuleNodePath}`;
};

// 导出cp 神壕node服务域名
export const nodeRichUrl = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    // return `${currConfig[`node_${osType}`].gray}${config.nodeRichPath}`;
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.nodeRichPath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.nodeRichPath}`;
    return `${currConfig[`node_${osType}`][env]}${config.nodeRichPath}`;
};

// 导出语音直播node服务域名
export const nodeLiveUrl = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.liveNodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.liveNodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.liveNodePath}`;
};

// 导出黑金大人物node服务域名
export const nodeBlackGoldUrl = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.blackGoldNodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.blackGoldNodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.blackGoldNodePath}`;
};

// 导出听听才艺node服务域名
export const nodeLiveTalentUrl = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    // return `${currConfig[`node_${osType}`].prod}${config.liveTalentNodePath}`;
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.liveTalentNodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.liveTalentNodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.liveTalentNodePath}`;
};

// 导出听听骑士榜node服务域名
export const nodeLiveKnightUrl = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.liveKnightNodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.liveKnightNodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.liveKnightNodePath}`;
};

// 导出听听人气榜node服务域名
export const nodeLiveHotUrl = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.liveHotNodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.liveHotNodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.liveHotNodePath}`;
};

// 导出Person1node服务域名
export const nodePerson1Url = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.Person1NodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.Person1NodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.Person1NodePath}`;
};

// 导出Person2node服务域名
export const nodePerson2Url = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.Person2NodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.Person2NodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.Person2NodePath}`;
};

// 导出Person3node服务域名
export const nodePerson3Url = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.Person3NodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.Person3NodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.Person3NodePath}`;
};

// 导出Person4node服务域名
export const nodePerson4Url = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.Person4NodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.Person4NodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.Person4NodePath}`;
};

// 导出Person5node服务域名
export const nodePerson5Url = function () {
    const { localNode } = parseUrlQuery();
    // 指向局域网下的后端ip
    if (localNode) return 'http://*************:8080';
    // 开发环境，指向特定的地址
    if (env === 'dev') return `${currConfig[`node_${osType}`].testing}${config.Person5NodePath}`;
    if (env === 'prod') return `${currConfig[`node_${osType}`].prod}${config.Person5NodePath}`;
    return `${currConfig[`node_${osType}`][env]}${config.Person5NodePath}`;
};

// 导出当前环境对应的页面域名
export const webUrl = () => {
    return `${currConfig[webKey()][env]}${config.projectName}/index.html`;
};

export const baseUrl = (_isShare = window.isShare) => {
    return `${currConfig[webKey(_isShare)][env]}`;
};

// 导出头像地址
export const avatarUrl = currConfig[`avatar_${osType}`][env];

// 短链协议 tt://
export const jsBridgeProtocol = currConfig.jsBridgeProtocol;

// 完整短链协议+域名 tt://m.52tt.com
export const jsBridgePreview = currConfig.jsBridgePreview;

// 官网地址
export const website = `${currConfig[`website_${osType}`]}`;

// 唤起app参数
export const activeAppOptions = currConfig.activeAppOptions;

// obs接口地址
export const obsDomain = 'https://obs.52tt.com/object/';
