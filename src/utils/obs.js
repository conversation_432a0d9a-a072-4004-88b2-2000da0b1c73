import { obsDomain } from '@/config/url';

/**
 * obs上传
 * @param {File} file 文件对象
 * @param {String} key 文件路径名称
 * @param {String} token obs token
 */
export const uploadObsFile = ({ file, key, token }) => {
    return new Promise((resolve) => {
        const data = new FormData();
        data.append('key', key);
        data.append('file', file);

        const xhr = new XMLHttpRequest();

        xhr.onload = () => {
            const resposnse = {
                ...JSON.parse(xhr.responseText),
                hash: xhr.getResponseHeader('etag'), // 从响应头获取文件hash值
            };
            resolve([resposnse]);
        };

        xhr.onerror = () => {
            resolve([undefined, JSON.parse(xhr.responseText)]);
        };

        xhr.open('post', obsDomain);
        xhr.setRequestHeader('Authorization', 'OBS ' + token);
        xhr.send(data);
    });
};
