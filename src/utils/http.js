import axios from 'axios';
import { sentryReport, httpErrorToast, filterChild } from '../utils/httpErrorToast';
import { nodeUrl } from '@/config/url';
import defaultConfig from '@/config';

const getToken = () => {
    try {
        return TTJSBridge.invoke('data', 'getToken');
    } catch (e) {
        return null;
    }
};

const createAxios = (axiosConfig = {}) => {
    const ins = axios.create(Object.assign({}, { timeout: 5000 }, axiosConfig));
    let toastOff = false;
    // 请求拦截器
    ins.interceptors.request.use(
        (config) => {
            config.url = config.baseNodeUrl + config.url;
            // 添加token
            const token = getToken();
            if (config.method === 'get') {
                config.params = Object.assign({}, config.params, { token });
            } else if (config.method === 'post') {
                config.data = Object.assign({}, config.data, { token });
            }
            config.headers['Authorization'] = token;
            // toastOff
            toastOff = config.toastOff;
            // 具体报错url提示开关
            return config;
        },
        (error) => {
            // mock 数据
            // if (window.myWebview.params.urlParams.mock) {
            //     return mock[error.config.mockUrl];
            // }
            httpErrorToast(error);
            return Promise.reject(error);
        },
    );

    // 响应拦截器
    ins.interceptors.response.use(
        (response) => {
            // mock 数据
            // if (window.myWebview.params.urlParams.mock) {
            //     return mock[response.config.mockUrl];
            // }
            const data = response.data;
            if (data.code !== 0 && !toastOff && defaultConfig.__isOffline.replace('flag', '') !== '____ActivityOfflineTag____') {
                const text = !data.msg && !data.code ? '服务器开小差了，请稍后重试~' : `${data.msg} (${data.code})`;
                f7toast.showError(text);
            }
            if (data.code !== 0) {
                // sentryReport({
                //     url: response.config.baseURL + response.config.url,
                //     method: response.config.method,
                //     status: response.status,
                //     params: filterChild(response.config.method === 'post' ? response.config.data : response.config.params, ['token']),
                //     data: response.data,
                // });
            }
            return data;
        },
        (error) => {
            // mock 数据
            // if (window.myWebview.params.urlParams.mock) {
            //     return mock[error.config.mockUrl];
            // }
            httpErrorToast(error);
            return Promise.reject(error);
        },
    );

    return ins;
};

const http = createAxios({
    baseURL: '',
    baseNodeUrl: nodeUrl(),
});

export { createAxios };
export default http;
