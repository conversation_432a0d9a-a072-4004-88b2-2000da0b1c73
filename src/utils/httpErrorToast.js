import { getENV } from '@/config/url';
import config from '../config';


const isProd = getENV() === 'prod';
/**
 * @name sentryReport 上报请求错误
 * @typedef {object} Metadata
 * @property {string} url api接口路径
 * @property {string} method 请求方法
 * @property {string | number} status 状态码
 * @property {string=} errorMsg 错误信息
 * @param {Metadata} metadata 上报信息
 */
export const sentryReport = (metadata) => {
    
};


export const filterChild = (source, arr) => {
    const target = JSON.parse(source);
    if (!arr.length > 0) return target;
    arr.forEach((element) => {
        if (element in target) delete target[element];
    });
    return target;
};

export const httpErrorToast = (error) => {
    if (config.__isOffline === '____52ttActivityOfflineTag____') {
        return;
    }
    const requestUrl = error?.config?.url || {};
    if (error.response) {
        const text = isProd ? `服务器开小差了，请稍后重试~` : `服务器开小差了，请稍后重试~[${requestUrl} ]`;
        f7toast.showError(text);
        sentryReport({
            url: error.config.baseURL + error.config.url,
            method: error.config.method,
            status: error.response.status,
            params: filterChild(error.config.method === 'post' ? error.config.data : error.config.params, ['token']),
            data: error.response.data,
        });
    } else if (error.request) {
        const text = isProd ? `请求超时了，请稍后重试~` : `请求超时了，请稍后重试~ [${requestUrl} - timeout - ${Date.now()}]`;
        f7toast.showError(text);
        sentryReport({ url: error.config.baseURL + error.config.url, status: 'timeout', method: error.config.method });
    } else {
        const text = isProd ? `${error.message}` : `${error.message} [${requestUrl} - ${Date.now()}]`;
        f7toast.showError(text);
        sentryReport({
            url: error.config.baseURL + error.config.url,
            method: error.config.method,
            status: 'timeout',
            errorMsg: error.message,
        });
    }
};
