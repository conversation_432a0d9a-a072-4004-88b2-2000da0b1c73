import { getCurrentHub } from '@sentry/browser';
function isPlainObject(what) {
    return Object.prototype.toString.call(what) === '[object Object]';
}

function formatComponentName(vm) {
    if (vm.$root === vm) {
        return 'root instance';
    }
    const name = vm._isVue ? vm.$options.name || vm.$options._componentTag : vm.name;
    return (name ? `component <${name}>` : 'anonymous component') + (vm._isVue && vm.$options.__file ? ` at ${vm.$options.__file}` : '');
}

export default {
    install(Vue, options = { attachProps: false, logErrors: false }) {
        if (!window.__SENTRY__) {
            console.error('[report error] 未引入Sentry');
            return;
        }
        const _Sentry = window.__SENTRY__;
        const { attachProps, logErrors } = options;
        Vue.config.errorHandler = (error, vm, info) => {
            const metadata = {};

            if (isPlainObject(vm)) {
                metadata.componentName = formatComponentName(vm);

                if (attachProps) {
                    metadata.propsData = vm.$options.propsData;
                }

                if (info !== void 0) {
                    metadata.lifecycleHook = info;
                }

                // This timeout makes sure that any breadcrumbs are recorded before sending it off the sentry
                setTimeout(() => {
                    getCurrentHub().withScope(scope => {
                        scope.setContext('vue', metadata);
                        getCurrentHub().captureException(error);
                    });
                });

                if (logErrors) {
                    Vue.util.warn(`Error in ${info}: "${error.toString()}"`, vm);
                    console.error(error);
                }
            }
        };

        /**
         * @name report
         * @param {Error} error
         * @param {Object} context 错误附加内容{name, data}
         */
        window.__SENTRY__.report = (error, context) => {
            setTimeout(() => {
                getCurrentHub().withScope(scope => {
                    if (context) {
                        scope.setContext(context.name, context.data);
                    }
                    getCurrentHub().captureException(error);
                });
            });
        };

        Vue.prototype.$sentry = Sentry;
    },
};
