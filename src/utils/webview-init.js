import BylinkSDK from '@tt/bylink-sdk';
import config from '../config';
import { params2search, filterParams } from '../utils/utils';
import { jsBridgePreview } from '@/config/url';

/**
 * define webview func
 * @returns {myWebviewInit}
 */
const myWebviewInit = function () {
    return this;
};

export const parseUrlQuery = (url = window.location.href) => {
    const result = /.+\?(.+)$/.exec(url);
    if (!result) return {};
    const paramsStr = result ? result[1] : ''; // 将 ? 后面的字符串提出来
    const paramsArr = paramsStr.split('&');
    const theRequest = paramsArr.reduce((pre, param) => {
        if (/=/.test(param)) {
            const splitIdx = param.indexOf('='); // 切割 key 和 value
            const key = param.substring(0, splitIdx);
            let val = param.substring(splitIdx + 1);
            val = decodeURIComponent(val); // url解码
            val = /^\d+$/.test(val) ? parseFloat(val) : val; // 判断是否是数字
            pre[key] = val; // 默认取同名 key 的最后一个 value
        } else {
            if (!!param) pre[param] = true; // 处理没有 value 的参数
        }
        return pre;
    }, {});

    try {
        const uid = TTJSBridge.invoke('data', 'getMyUid');
        theRequest.uid = theRequest.uid || uid;
    } catch (e) {}

    return theRequest;
};

myWebviewInit.prototype = {
    params: {
        urlParams: parseUrlQuery(window.location.href),
    },

    // 判断是否在微信内
    isInWx: function () {
        let ua = navigator.userAgent.toLowerCase();
        return !!ua.match(/MicroMessenger/i);
    },

    //判断是在qq内
    isInQq: function () {
        let ua = navigator.userAgent.toLowerCase();
        return !!ua.match(/ qq\//i); //qq的userAgent的前面有个空格，后面有个斜杆
    },

    //是否在APP内
    isInApp: function () {
        try {
            TTJSBridge.invoke('operate', 'isInApp');
            return true;
        } catch (e) {
            return false;
        }
    },

    //是否在SDK内
    isInSdk: function () {
        if (this.params.urlParams.hasOwnProperty('app') && this.params.urlParams.app == 12) {
            return true;
        } else {
            return false;
        }
    },

    // 是否IOS
    isIOS: function () {
        if (this.params.urlParams.hasOwnProperty('os_type')) {
            return parseInt(this.params.urlParams.os_type, 10) === 2;
        } else {
            let ua = navigator.userAgent;
            return !!ua.match(/(iPhone\sOS|iOS)\s([\d_]+)/i);
        }
    },
};

/**
 * define myWebview
 * @type {myWebviewInit}
 */
const myWebview = new myWebviewInit();

/**
 * @name getToken 获取token
 */
export const getToken = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('data', 'getToken');
        } catch (e) {
            return null;
        }
    }
    return null;
};

/**
 * @name toRoom 跳转房间
 * @param {string} channel_id 房间号
 */
export const toRoom = (channel_id) => {
    console.log('toRoom', channel_id);
    if (myWebview.isInApp()) {
        try {
            if (!channel_id) return;
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/channel?channel_id=${channel_id}`);
        } catch (e) {}
    }
};

/**
 * @name toPerson 跳转用户信息
 * @param {string} account 账户
 */
export const toPerson = (account) => {
    console.log('toPerson', account);
    if (myWebview.isInApp()) {
        try {
            if (!account) return;
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/userdetail?account=${account}`);
        } catch (e) {}
    }
};

/**
 * @name toGuild 跳转公会信息
 * @param {string} id 公会ID
 */
export const toGuild = (id) => {
    console.log('toGuild', id);
    if (myWebview.isInApp()) {
        try {
            if (!id) return;
            TTJSBridge.invoke(
                'operate',
                'jump',
                `${jsBridgePreview}/guild_card?guild_id=${id}&hy_guild_id=${id}&mk_guild_id=${id}`,
            );
        } catch (e) {
            // console.log('toGuild', e);
        }
    }
};

/**
 * @name jumpLink 跳转链接
 * @param {string} link 链接
 */
export const jumpLink = (link) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', link);
        } catch (e) {}
    }
};

/**
 * @name jumpIm 跳转到IM
 * @param {number} account 账户
 * @param {number} nickname 昵称
 */
export const jumpIm = (account, nickname) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/chat?account=${account}`);
        } catch (e) {}
    }
};

/**
 * @name setRightTextVisibility 是否显示顶部bar栏右边文字
 * @param {boolean} $boolean
 */
export const setRightTextVisibility = ($boolean) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'setRightTextVisibility', $boolean);
        } catch (e) {}
    }
};

/**
 * @name updateRightItemView 设置顶部bar栏右边文字&样式
 * @param {string} text 文字
 * @param {string} color 字体颜色
 * @param {string} bgColor 背景颜色
 */
export const updateRightItemView = (text, color, bgColor) => {
    if (myWebview.isInApp) {
        try {
            let json = { text, textColor: color, text_bg_color: bgColor };

            TTJSBridge.invoke('ui', 'updateRightItemView', JSON.stringify(json));
        } catch (e) {
            // alert(e);
        }
    }
};

/**
 * @name updateRightText 设置顶部bar栏右边文字
 * @param {string} text
 */
export const updateRightText = (text) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'updateRightText', text);
        } catch (e) {
            // alert(e);
        }
    }
};

/**
 * @name setRightTextRunMethod 设置顶部bar栏右边文字点击方法回调
 * @param {function} method 回调方法
 */
export const setRightTextRunMethod = (method = '') => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'setRightTextRunMethod', '{"method" : ' + method + '}');
        } catch (e) {
            // alert(e);
        }
    }
};

/**
 * @name setRightButtonList 隐藏右上角三个点
 */
export const setRightButtonList = (list) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'setRightButtonList', JSON.stringify(list));
        } catch (e) {
            // alert(e);
        }
    }
};

/**
 * @name updateTitleBarMode 修改顶部title样式
 * @param {boolean} $boolean true:白色 false:黑色
 */
export const updateTitleBarMode = ($boolean) => {
    if (myWebview.isInApp) {
        try {
            TTJSBridge.invoke('ui', 'updateTitleBarMode', $boolean);
        } catch (e) {
            // alert(e);
        }
    }
};

/**
 * @name setGoBackInvokeMethod 修改返回按钮方法
 * @param {string} methodName 方法名 em：myWebview.share
 * @param {Array} args 参数数组
 */
export const setGoBackInvokeMethod = (methodName, args = []) => {
    if (myWebview.isInApp) {
        try {
            TTJSBridge.invoke(
                'ui',
                'setGoBackInvokeMethod',
                JSON.stringify({
                    methodKey: location.origin + location.pathname,
                    method: methodName,
                    params: [...args],
                }),
            );
        } catch (e) {
            // alert(e);
        }
    }
};

/**
 * @name updateBarColor 设置顶部bar背景色
 * @param {string} color 颜色
 */
export const updateBarColor = (color) => {
    if (myWebview.isInApp) {
        try {
            TTJSBridge.invoke('ui', 'updateBarColor', color); //顶部 bar背景色
        } catch (e) {
            // alert(e);
        }
    }
};

export const showToast = (parameters) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'showToast', parameters);
        } catch (e) {
            //webErrorReport('apiError', 'Show toast failed - ' + e.name + ": " + e.message);
        }
    }
};

/**
 * @name getDeviceId 获取设备号
 */
export const getDeviceId = () => {
    let deviceId;
    if (myWebview.isInApp()) {
        try {
            deviceId = TTJSBridge.invoke('data', 'getDeviceId');
        } catch (e) {
            // alert(e);
        }
    }
    return deviceId;
};

/**
 * @name toKaihei 跳转去开黑
 */
export const toKaihei = () => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/home?main_tab=channel&second_tab=sec_gang_up`);
        } catch (e) {
            // alert(e)
        }
    }
};

/**
 * @name toEquip 跳转到个性装扮
 * @param {number} selectTab 0 =>麦位框 1 =>坐骑 2 =>主页飘
 */
export const toEquip = (selectTab) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/userPersonalityDress?select_tab=${selectTab}`);
        } catch (e) {
            // alert(e)
        }
    }
};

/**
 * @name toChange // 跳转到充值页
 */
export const toChange = () => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'jump', `${jsBridgePreview}/tcoin`);
        } catch (e) {
            // alert(e)
        }
    }
};

/**
 * @name saveImg 保存到相册
 * @param {base64} base64 图片
 * @param {function} cb 回调函数
 */
export const saveImg = ({ base64, cb }) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'saveImgToGallery', JSON.stringify({ base64, imgType: 2 }), cb);
        } catch (e) {}
    }
};

/**
 * @name toPublishActivity 进入发布页
 * @param {object} param 参数
 */
export const toPublishActivity = (param) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('operate', 'toPublishActivity', JSON.stringify(param));
        } catch (e) {
            // alert(JSON.stringify(e));
        }
    }
};

/**
 * @name getVersion 获取App版本号
 */
export const getVersion = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('operate', 'getVersion');
        } catch (e) {}
    }
};

/**
 * @name getVersion 是否版本以上
 * @param {number} phoneType 0:安卓 1:iOS
 * @param {Array} specificVersion [安卓, ios] 例如['67108874', '4.0.9']
 * @param {function} cb 回调函数
 */
export const specificVersion = (phoneType, specificVersion, cb) => {
    if (myWebview.isInApp()) {
        try {
            let cuVer = myWebview.getVersion();
            let version = specificVersion;
            if (cuVer && phoneType && cuVer < version[phoneType]) {
                myWebview.showToast('当前版本不支持该操作，请升级最新版本');
            } else {
                if (cb) cb();
            }
        } catch (e) {
            // alert('specificVersion:' + JSON.stringify(e))
        }
    }
};

/**
 * @name checkInstallApp 检查是否安装APP
 * @param {string} appName Wechat | QQ | QZone
 * @returns {boolean}
 */
export const checkInstallApp = (appName) => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('operate', 'checkInstallApp', appName) === 'true';
        } catch (e) {
            return false;
        }
    }
    return false;
};

/**
 * @name finishActivity 关闭webview
 */
export const finishActivity = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('ui', 'finishActivity');
        } catch (e) {}
    }
};

/**
 * @name onBackPressed webview回退上一页
 */
export const onBackPressed = () => {
    if (myWebview.isInApp()) {
        try {
            return TTJSBridge.invoke('ui', 'onBackPressed');
        } catch (e) {}
    }
};

/**
 * @name enterFullScreen 进入全屏模式
 * @param {boolean} $boolean
 */
export const enterFullScreen = ($boolean) => {
    if (myWebview.isInApp()) {
        try {
            TTJSBridge.invoke('ui', 'enterfullscreen', $boolean);
        } catch (e) {
            //
        }
    }
};

/**
 * @name pageView 页面曝光上报
 * @param {string} pageId
 * @param {string} event
 */
export const pageView = (pageId, source = '') => {
    if (!window.bylink) {
        window.bylink = new BylinkSDK(config.projectName, false);
    }
    window.bylink.pageView(pageId, source);
};

/**
 * @name track 统计上报事件
 * @param {BylinkProperties} properties
 * @param {string} event
 */
export const track = (properties, event = 'click') => {
    if (!window.bylink) {
        window.bylink = new BylinkSDK(config.projectName, false);
    }
    window.bylink.track(properties, event);
};

export const pushUrlParams = (obj = {}) => {
    history.replaceState(
        {},
        '',
        location.origin + location.pathname + params2search(Object.assign({}, myWebview.params.urlParams, obj)),
    );
};

export const filterUrlParams = (list = []) => {
    history.replaceState(
        {},
        '',
        location.origin + location.pathname + params2search(filterParams(myWebview.params.urlParams, list)),
    );
};

export const shareFriend = (uid) => {
    if (myWebview.isInApp()) {
        const locationUrl = `${window.location.protocol}//${window.location.host}${window.location.pathname}`;
        const shareData = {
            share_type: 'TT',
            ttShareMsgType: 0,
            title: '快来和我组CP吧～',
            content: '快来和我组CP吧～',
            url: `${locationUrl}?cpuid=${uid}`,
            imageUrl: `https://ga-album-cdnqn.52tt.com/web/anniversary-guild-match-2020/20201215173655_56339602.png`, // 图文里的缩略图url
            imagePath: '', // 纯图片类型（仅支持手机本地地址）
            label: '', // 入口标签名
            musicUrl: '',
        };
        try {
            TTJSBridge.invoke('ui', 'thirdPlatformShare', JSON.stringify(shareData));
        } catch (err) {
            // alert(err)
        }
    }
};

export default myWebview;
