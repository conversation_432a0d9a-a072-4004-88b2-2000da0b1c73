export const toast = function (f7) {
    function getIcon(type = 'check') {
        // 目前支持icon有info:普通信息⚠️，check: ✅，error: 错误❎, 后续可自行新增
        return `<img class="w-[36px] object-contain m-center" src="${require(`/static/img/${type}_no_compress.png`)}" />`;
    }

    function getPayload(type) {
        const payload = {
            icon: getIcon(type),
            // on: {
            //     open: function () {
            //         const tc = document.querySelector('.toast-content');
            //         tc.classList.add('toast-content-custom');
            //     },
            //     closed: function () {
            //         const tc = document.querySelector('.toast-content');
            //         tc.classList.remove('toast-content-custom');
            //     },
            // },
        };
        return payload;
    }
    let realOptions = {};

    function showIcon(options, type) {
        if (typeof options === 'string') {
            defaultOptions.text = options;
            realOptions = Object.assign({}, defaultOptions, getPayload(type));
        } else if (typeof options === 'object') {
            realOptions = Object.assign({}, defaultOptions, options, getPayload(type));
        }
        f7.toast.show(realOptions);
    }

    let defaultOptions = {
        closeButton: true,
        closeTimeout: 2000,
    };
    return {
        create(options) {
            try {
                realOptions = Object.assign({}, defaultOptions, options);
                f7.toast.create(defaultOptions);
            } catch (error) {
                console.error(error);
            }
        },
        close(el, isAnimated) {
            f7.toast.close(el, isAnimated);
        },
        destroy(el, isAnimated) {
            f7.toast.destroy(el, isAnimated);
        },
        show(options) {
            if (typeof options === 'string') realOptions = Object.assign({}, defaultOptions, { text: options });
            else if (typeof options === 'object') {
                const { type, ...rest } = options;
                if (type) {
                    const payload = getPayload(type);
                    realOptions = Object.assign({}, defaultOptions, rest, payload);
                } else realOptions = Object.assign({}, defaultOptions, rest);
            }
            f7.toast.show(realOptions);
        },
        showInfo(options) {
            showIcon(options, 'info');
        },
        showCheck(options) {
            showIcon(options, 'check');
        },
        showError(options) {
            showIcon(options, 'error');
        },
    };
};

export const preloader = function (f7) {
    // color:  https://framework7.io/docs/color-themes.html
    return {
        show(color = 'white') {
            f7.preloader.show(color);
        },
        hide() {
            f7.preloader.hide();
        },
        showIn(el, color = 'black') {
            try {
                f7.preloader.show(el, color);
            } catch (error) {
                console.error(error);
            }
        },
        hideIn(el) {
            try {
                f7.preloader.show(el);
            } catch (error) {
                console.error(error);
            }
        },
    };
};

export const modal = function (f7) {
    return function (id, type = 'dialog', options = {}) {
        try {
            if (Object.keys(options).length) {
                f7[type].open(f7.$(id)).emit('setData', options);
            } else f7[type].open(f7.$(id));
        } catch (error) {
            console.error('open modal error:', error);
        }
    };
};
