// 截取结点图片base64，传入对应的ref
import { getVersion, toPublishActivity } from '@/utils/webview-init';

let imgMixin = {
    methods: {
        async getImageBase64(target, params = {}) {
            if (!target) {
                f7toast.show('请输入目标ref');
                console.error('请输入目标ref');
                return;
            }
            f7preloader.show();
            if (this.isInApp && this.isIOS && getVersion() < 362) {
                f7toast.show('当前客户端版本不支持该操作，请升级最新版本');
                f7preloader.hide();
                return;
            }
            const cropImg = this.$refs[target];
            const height = cropImg.offsetHeight;
            const { timeout = 15000, scale = 3, backgroundColor = null } = params;
            const options = {
                useCORS: true,
                height,
                imageTimeout: timeout,
                backgroundColor,
                scale,
                ignoreElements(e) {
                    if (typeof e.className === 'string' && e.className.match('ignore')) {
                        return true;
                    }
                    return false;
                },
            };
            try {
                const canvas = await html2canvas(cropImg, options);
                const cropImage = canvas.toDataURL('image/png', 1);
                console.log('cropImage', cropImage);
                return cropImage;
            } catch (error) {
                f7toast.show('生成图片失败');
                console.error('生成图片base64失败', error?.message.toString());
                f7preloader.hide();
            }
        },
        async saveCardByBase64(imageBase64) {
            return new Promise((resolve, reject) => {
                let toast = true; // 超时标志位
                if (this.isInApp) {
                    myWebview.downloadPic = async (dataStr) => {
                        const result = JSON.parse(dataStr);
                        const { imgPath } = result;
                        if (!imgPath) {
                            toast = false; // 不设置超时提示
                            f7preloader.hide();
                            f7toast.show('保存失败');
                            reject();
                        } else {
                            toast = false; // 不设置超时提示
                            f7preloader.hide();
                            f7toast.show('保存成功');
                            resolve(imgPath);
                        }
                    };
                    try {
                        // 处理ios第一次访问授权未关闭弹窗
                        setTimeout(() => {
                            f7preloader.hide();
                            if (toast) {
                                f7toast.show('保存超时，请重试');
                            }
                        }, 7000);
                        TTJSBridge.invoke(
                            'operate',
                            'saveImgToGallery',
                            JSON.stringify({
                                base64: imageBase64,
                                imgType: 2,
                            }),
                            'myWebview.downloadPic',
                        ); // 新接口
                    } catch (e) {
                        f7preloader.hide();
                    }
                } else {
                    f7toast.show('端外不可以操作');
                    f7preloader.hide();
                }
            });
        },
        sendImageToIM(imgPath) {
            try {
                const shareData = {
                    share_type: 'TT',
                    ttShareMsgType: 1, // 1图片，其他（图文）0 默认
                    title: '',
                    content: '',
                    url: '', // 图文调整链接
                    imageUrl: '', // 图文里的缩略图url
                    imagePath: imgPath, // 纯图片类型（仅支持手机本地地址）
                    label: '', // 入口标签名
                    musicUrl: '',
                };
                f7preloader.show();
                TTJSBridge.invoke('ui', 'thirdPlatformShare', JSON.stringify(shareData)); // 新接口
                f7preloader.hide();
            } catch (error) {
                f7toast.show(`发送失败：${error.toString()}`);
                f7preloader.hide();
            }
        },
        async sendActivityWithImg(imgPath, subTopicName = '晒一晒我的猫猫队') {
            if (window.myWebview.isIOS()) {
                toPublishActivity({
                    attachmentList: [
                        {
                            type: 1,
                            path: imgPath,
                        },
                    ],
                    pageType: 2,
                    subTopicName,
                });
            } else {
                toPublishActivity({
                    attachmentList: [
                        {
                            type: 1,
                            path: imgPath,
                        },
                    ],
                    pageType: 5,
                    subTopicName,
                });
            }
        },
    },
};

export default imgMixin;
