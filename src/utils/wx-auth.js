import axios from 'axios';

const WX_APP_ID = 'wx9cf924351316d70f';
const SERVER_HOST = 'https://node.52tt.com/common-production/wx-base/api/wechat/';

/**
 * @name getUrlParams 获取URL上的参数
 * @returns {object}
 */
const getUrlParams = () => {
    let url = location.search;
    let urlParams = {};
    if (url.indexOf('?') != -1) {
        let str = url.substr(1);
        let strs = str.split('&');
        for (let i = 0; i < strs.length; i++) {
            urlParams[strs[i].split('=')[0]] = unescape(strs[i].split('=')[1]);
        }
    }
    return urlParams;
};

/**
 * @name filterParams 过滤参数中指定的key
 * @param {object} params 参数对象
 * @param {array} list 需过滤的key列表
 */
const filterParams = (params, list = []) => {
    if (params === {} || Object.keys(params).length < 1) return {};
    const res = Object.assign({}, params);
    Object.keys(res).forEach(key => {
        if (list.indexOf(key) !== -1) {
            delete res[key];
        }
    });
    return res;
};

/**
 * @name params2search 参数对象转UrlSearch
 * @param {object} params 参数对象
 */
const params2search = params => {
    if (!params || Object.keys(params).length < 1) return '';
    return `?${Object.keys(params)
        .reduce((accumulator, currentValue) => `${accumulator}&${currentValue}=${params[currentValue]}`, '')
        .slice(1)}`;
};

/**
 * @name pushUrlParams 更新location状态
 * @param {*} obj
 */
const pushUrlParams = (obj = {}) => {
    history.replaceState({}, '', location.origin + location.pathname + params2search(obj));
};

/**
 * @name wxAuth 微信授权
 * @param {string} appid 公众号appi
 * @param {string} redirect 回调地址，默认是当前地址
 */
export const wxAuth = (appid = WX_APP_ID) => {
    const redirect = location.origin + location.pathname + params2search(filterParams(getUrlParams(), ['code', 'state']));
    // 获取code
    window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${encodeURIComponent(
        redirect,
    )}&response_type=code&scope=snsapi_userinfo&state=7#wechat_redirect`;
};

/**
 * @name getUserInfoByCode 获取用户信息(code)
 * @param {string} appid
 * @param {string} code
 * @returns {object} 用户信息
 */
export const getUserInfoByCode = async (code, appid = WX_APP_ID) => {
    try {
        const { data } = await axios.post(
            `${SERVER_HOST}wxGrantNotice`,
            { appid, code },
            {
                withCredentials: true,
            },
        );
        return data;
    } catch (e) {
        // console.log(e);
        return false;
    }
};

/**
 * @name getUserInfoByCookies 获取用户信息(Cookies)
 * 由服务器来检查所携带的Cookies是否有效，来返回用户信息
 * @returns {object} 用户信息
 */
export const getUserInfoByCookies = async () => {
    try {
        const { data } = await axios.get(`${SERVER_HOST}userinfo`, {
            withCredentials: true,
        });
        return data;
    } catch (e) {
        console.log(e);
        return false;
    }
};

/**
 * @name wxSDKInit 初始化微信SDK
 * @param {array} sdkApiList 微信sdk api开启列表 https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#63
 * @returns {Promise}
 */
export const wxSDKInit = (sdkApiList, appid = WX_APP_ID, url = window.location.href.split('#')[0]) => {
    return new Promise(async (resolve, reject) => {
        try {
            const { data } = await axios.post(`${SERVER_HOST}signature`, {
                appid,
                url,
            });
            if (data.code === 0) {
                const res = data.data;
                wx.config({
                    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                    appId: appid, // 必填，公众号的唯一标识
                    timestamp: res.timestamp, // 必填，生成签名的时间戳
                    nonceStr: res.noncestr, // 必填，生成签名的随机串
                    signature: res.signature, // 必填，签名
                    jsApiList: sdkApiList, // 必填，需要使用的JS接口列表
                    openTagList: ["wx-open-launch-app"], // 使用微信开放标签
                });
                wx.ready(function() {
                    // console.log('wechat init succeed!');
                    resolve();
                });
                wx.error(function(res) {
                    // console.log('wechat init failed!', res);
                    reject();
                });
            } else {
                reject();
            }
        } catch (e) {
            // console.log(e);
            reject();
        }
    });
};

/**
 * @name wxInit 微信初始化
 * @param {array} sdkApiList 微信sdk api开启列表 https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#63
 * @returns { object } 返回微信用户信息 {code, msg, data}
 */
export const wxInit = async (sdkApiList, appid = WX_APP_ID) => {
    let initRes = { code: 0, msg: 'Waiting auth.', data: {} };
    const urlParams = getUrlParams();
    // 当连接上带有微信的code
    if (urlParams.code) {
        const codeRes = await getUserInfoByCode(urlParams.code, appid);
        if (codeRes) {
            initRes = codeRes;
        } else {
            return { code: -1, msg: 'Get userinfo failed by code.', data: {} };
        }
    } else {
        // 检查cookies的有效性
        const cookiesRes = await getUserInfoByCookies();
        if (cookiesRes) {
            if (cookiesRes.data && cookiesRes.data.regrant) {
                wxAuth(); // 拉起授权弹窗
                return initRes;
            }
            initRes = cookiesRes;
        } else {
            return { code: -2, msg: 'Get userinfo failed by cookies.', data: {} };
        }
    }

    try {
        await wxSDKInit(sdkApiList, appid);
        if (urlParams.code) {
            pushUrlParams(filterParams(getUrlParams(), ['code', 'state'])); // 清除微信code和state参数
        }
        return initRes;
    } catch (e) {
        return { code: -3, msg: 'Wechat SDK init failed.', data: initRes };
    }
};
