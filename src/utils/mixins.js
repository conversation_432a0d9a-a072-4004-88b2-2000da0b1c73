import { toPerson, toRoom, toGuild, parseUrlQuery } from '@/utils/webview-init';
import config from '@/config';

let mixin = {
    computed: {
        isInApp() {
            return window.myWebview.isInApp();
        },
        isIOS() {
            return window.myWebview.isIOS();
        },
        beans() {
            return window.KEY === 'tt' ? 'T豆' : '豆豆';
        },
        isEnd() {
            return this.$store.getters.isEnd;
        },
        initData() {
            return this.$store.state.initData || {};
        },
        showStatusBar() {
            // 增加测试环境
            const { immersion, isDev } = parseUrlQuery();
            return (immersion && this.isInApp) || (immersion && isDev);
        },
        isRevenueBegin() {
            return this.$store.getters.isRevenueBegin;
        },
    },
    methods: {
        handleAvatar(account, type = 1) {
            if (!account) {
                return;
            }
            // if (!window.myWebview.isInApp()) {
            //     window.activeApp.open();
            //     return;
            // }
            switch (type) {
                case 1:
                    toPerson(account);
                    break;
                case 2:
                    toRoom(account);
                    break;
                case 3:
                    toGuild(account);
                    break;
                default:
                    break;
            }
        },
        avatarError(event) {
            event.target.src = require('/static/img/default_avatar_no_compress.png');
        },
        openModal(id, type, options) {
            f7modal(id, type, options);
        },
        closeModal(id, type = 'dialog') {
            f7[type].close(f7.$(id));
        },
        toAppointPage(value, defaultValue = 0) {
            let list = config.pageSections;
            if (!this.isRevenueBegin) {
                list = config.visiblePages;
            }
            const index = list.findIndex((item) => item === value);
            if (index >= 0) {
                this.$fp.moveTo(index + 1);
            } else if (defaultValue) {
                this.$fp.moveTo(defaultValue);
            }
        },
    },
};
export default mixin;
