import Tween from '@tt/tween';
import { avatarUrl, getENV } from '@/config/url';
/**
 * stopDoubleClick: 防止重复触发
 * getUrlParams: 提取URL上的query
 * formatTime: 时间格式化
 * getAvatar: 获取头像地址
 * omitValue: 科学计数转换
 * countdown: 倒计时
 * sleep: 休眠
 * requestAnimationFrameFn: 轮播
 * findIndex: 获取下标
 * isIphoneX: 判断是否iphoneX
 * isInENV: 根据当前URL判断当前环境是否在指定环境列表中
 * loadScriptSync: 加载JS脚本
 * filterParams: 过滤参数中指定的key
 * params2search: 参数对象转UrlSearch
 * scrollToAnchor: 滚动到锚点
 * downloadImage: 下载图片
 * num2percent: 数字转百分比
 * supplementDouble: 补齐数值两位
 * getByteLen: 判断字符串的字节长度
 * sliceByte: 按字节切割字符串
 */

/**
 * @name stopDoubleClick 防止重复触发按钮
 * @param {Vue} vm vue组件的实例
 * @param {number} delay 按钮禁用的延迟时间
 * @return {boolean} 是否处于点击中
 * @example if (!stopDoubleClick(this, 500)) return;
 */
export const stopDoubleClick = (vm, delay) => {
    if (vm.isVmClick) {
        return false;
    }
    vm.isVmClick = true;
    setTimeout(
        () => {
            vm.isVmClick = false;
        },
        delay ? delay : 1000,
    );
    return true;
};

/**
 * @name getUrlParams 获取URL上的参数
 * @returns {object}
 */
export const getUrlParams = (url = window.location.href) => {
    const paramsStrAll = /.+\?(.+)$/.exec(url);
    if (!paramsStrAll) return {};
    const paramsStr = /.+\?(.+)$/.exec(url)[1]; // 将 ? 后面的字符串提出来
    const paramsArr = paramsStr.split('&');
    return paramsArr.reduce((pre, param) => {
        if (/=/.test(param)) {
            const splitIdx = param.indexOf('='); // 切割 key 和 value
            const key = param.substring(0, splitIdx);
            let val = param.substring(splitIdx + 1);
            val = decodeURIComponent(val); // url解码
            val = /^\d+$/.test(val) ? parseFloat(val) : val; // 判断是否是数字
            pre[key] = val; // 默认取同名 key 的最后一个 value
        } else {
            if (!!param) pre[param] = true; // 处理没有 value 的参数
        }
        return pre;
    }, {});
};

/**
 * @name formatTime 转换为时间格式方法自定义方式
 * @param {number} value 时间戳
 * @param {string} format 格式比如"yyyy-MM-dd hh:mm:ss"
 * @param {string} unit 时间戳单位 ['s' | 'ms']
 * @returns {string}
 */
export const formatTime = (value = Date.now(), format = 'yyyy-MM-dd', unit = 's') => {
    let distance = new Date().getTimezoneOffset();
    /* eslint-disable */
    distance = distance + 8 * 60;
    if (unit === 's') value *= 1000;
    value += distance * 60 * 1000;
    let time = new Date(parseInt(value));
    let targetFormat = format ? format : 'yyyy-MM-dd h:m:s';
    let date = {
        'Y+': time.getFullYear(),
        'M+': time.getMonth() + 1,
        'd+': time.getDate(),
        'h+': time.getHours(),
        'm+': time.getMinutes(),
        's+': time.getSeconds(),
        'q+': Math.floor((time.getMonth() + 3) / 3),
        'S+': time.getMilliseconds(),
    };
    if (/(y+)/i.test(targetFormat)) {
        targetFormat = targetFormat.replace(RegExp.$1, (time.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (let k in date) {
        if (new RegExp('(' + k + ')').test(targetFormat)) {
            targetFormat = targetFormat.replace(RegExp.$1, RegExp.$1.length == 1 ? date[k] : ('00' + date[k]).substr(('' + date[k]).length));
        }
    }
    return targetFormat;
};

/**
 * @name formatTxt 省略文本
 * @param {string} txt 文本
 * @param {number} length 显示长度
 * @returns {string} 省略后的文本
 */
export const omitTxt = (txt, length = 5) => {
    return txt !== undefined && txt.length > length ? `${txt.substring(0, length)}...` : txt;
};

/**
 * @name getAvatar 获取头像
 * @param {number | string} account ttid/房间id/公会id
 * @param {number} type 头像类型 1:用户头像 2:房间头像 3:公会头像
 * @returns {string} 头像地址
 */
export const getAvatar = (account, type = 1) => {
    const env = getENV();
    const extra = type === 2 ? '@channel' : type === 3 ? '@guild' : '';
    // 灰度跟正式需要加上size=small后缀
    return `${avatarUrl}${account || 0}${extra}${['dev', 'testing'].includes(env) ? '' : '?size=small'}`;
};

/**
 * @name omitValue 省略数值
 * @param {number} val 数值
 * @param {number} threshold 阈值 默认10000
 * @param {string} txt 省略单位 默认'w'
 */
export const omitValue = (val, threshold = 10000, txt = 'w') => {
    return val >= threshold ? Math.floor(val / 100) / 100 + txt : val;
};

/**
 * @name countdown
 * @param {number} endTime 秒数
 * @returns [天,时,分,秒] 字符串数组
 */
export const countdown = (endTime) => {
    let time = endTime;
    let day = parseInt(time / (60 * 60 * 24));
    let hour = parseInt((time % (60 * 60 * 24)) / (60 * 60));
    let minute = parseInt((time % (60 * 60)) / 60);
    let second = parseInt(time % 60);
    day = day >= 10 ? `${day}` : `0${day}`;
    hour = hour >= 10 ? `${hour}` : `0${hour}`;
    minute = minute >= 10 ? `${minute}` : `0${minute}`;
    second = second >= 10 ? `${second}` : `0${second}`;
    return [day, hour, minute, second];
};

/**
 * @name sleep 休眠
 * @param {number} ms 休眠时间（毫秒）
 * @returns {Promise}
 */
export const sleep = async (ms) => {
    return new Promise(function (resolve, reject) {
        setTimeout(function () {
            resolve(true);
        }, ms);
    });
};

/**
 * @name requestAnimationFrame 左右无缝轮播
 * 兼容requestAnimationFrame
 */
export const requestAnimationFrameFn = () => {
    let lastTime = 0,
        nextFrame,
        cancelFrame;
    nextFrame =
        window.requestAnimationFrame ||
        window.webkitRequestAnimationFrame ||
        window.mozRequestAnimationFrame ||
        window.msRequestAnimationFrame ||
        function (callback) {
            let currTime = +new Date(),
                delay = Math.max(1000 / 60, 1000 / 60 - (currTime - lastTime));
            lastTime = currTime + delay;
            return setTimeout(callback, delay);
        };
    cancelFrame =
        window.cancelAnimationFrame ||
        window.webkitCancelAnimationFrame ||
        window.webkitCancelRequestAnimationFrame ||
        window.mozCancelRequestAnimationFrame ||
        window.msCancelRequestAnimationFrame ||
        clearTimeout;
    return [nextFrame, cancelFrame];
};

export const findIndex = (array, predicate, context) => {
    for (let i = 0; i < array.length; i++) {
        if (predicate.call(context, array[i], i, array)) return i;
    }
    return -1;
};

/**
 * @name isIphoneX 判断是否是iphoneX
 * @returns {object}
 */
export const isIphoneX = () => {
    let iphone = navigator.userAgent.indexOf('iphone') > -1;
    let isIphoneX = window.devicePixelRatio && window.devicePixelRatio === 3 && window.screen.width === 375 && iphone;
    return isIphoneX;
};

/**
 * @name isInENV 根据当前URL判断当前环境是否在指定环境列表中
 * @param {Array<string>} list 指定环境列表
 * ['prod', 'gray', 'testing', 'internal', 'dev']
 */
export const isInENV = (list) => {
    return list.indexOf(getENV()) > -1;
};

/**
 * @name loadScriptSync 加载JS脚本
 * @param {string | Array} src 脚本地址
 * @returns {Promise} 返回一个Promis.all
 */
export const loadScriptSync = (src) => {
    let params = src;
    if (typeof src === 'string') params = [src];
    const scripts = [];
    for (let i = 0; i < params.length; i++) {
        scripts[i] = new Promise((resolve, reject) => {
            const HEAD = document.getElementsByTagName('head')[0] || document.documentElement;
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.onload = () => {
                resolve(script);
            };
            script.onerror = () => {
                reject(false);
            };
            script.src = params[i];
            HEAD.appendChild(script);
        });
    }
    return Promise.all(scripts);
};

/**
 * @name loadRemoteFile 加载远程文件,json
 * @param {String} url 远程资源地址
 * @returns {promise} 返回一个promise
 */
export const loadRemoteFile = (url) => {
    return new Promise((resolve, reject) => {
        const rawFile = new XMLHttpRequest();
        rawFile.overrideMimeType('application/json');
        rawFile.open('GET', url, true);
        rawFile.onreadystatechange = function () {
            if (rawFile.readyState === 4 && rawFile.status == '200') {
                resolve(rawFile.responseText);
            }
        };
        rawFile.send(null);
    });
};

/**
 * @name filterParams 过滤参数中指定的key
 * @param {object} params 参数对象
 * @param {array} list 需过滤的key列表
 */
export const filterParams = (params, list = []) => {
    if (params === {} || Object.keys(params).length < 1) return {};
    const res = Object.assign({}, params);
    Object.keys(res).forEach((key) => {
        if (list.indexOf(key) !== -1) {
            delete res[key];
        }
    });
    return res;
};

/**
 * @name params2search 参数对象转UrlSearch
 * @param {object} params 参数对象
 */
export const params2search = (params) => {
    if (!params || Object.keys(params).length < 1) return '';
    return `?${Object.keys(params)
        .reduce((accumulator, currentValue) => `${accumulator}&${currentValue}=${params[currentValue]}`, '')
        .slice(1)}`;
};

/**
 * @name scrollToAnchor 滚动到锚点
 * @param {string} contentSelector 滚动容器的选择器
 * @param {string} domSelector 锚点DOM的选择器ID
 */
export const scrollToAnchor = (contentSelector, domSelector, distance = 0) => {
    if (!window.tween) window.tween = new Tween();
    const view = document.querySelector(contentSelector);
    const dom = document.querySelector(domSelector);
    const startTop = contentSelector === '.progress-wrapper' ? 0 : view.scrollTop;
    const orderTop = startTop + dom.getBoundingClientRect().top - distance;
    window.tween.animation(
        startTop,
        orderTop,
        (value) => {
            view.scrollTop = value;
        },
        500,
        Tween.EASING_TYPE.QUAD_EASEINOUT,
    );
};

/**
 * @name downloadImage 下载图片
 * @param {string} path 图片地址
 */
export const downloadImage = (path) => {
    return new Promise((resolve, reject) => {
        const image = new Image();
        image.onload = resolve;
        image.onerror = reject;
        image.src = path;
    });
};

/**
 * @name num2percent 数字转百分比
 * @param {number} num
 */
export const num2percent = (num) => {
    return num === 0 ? '0%' : `${Math.floor(num * 100)}%`;
};

/**
 * @name supplementDouble 补齐数值两位
 * @param {number | string} num
 */
export const supplementDouble = (num) => {
    const n = parseInt(num, 10);
    return n > 0 ? (n <= 9 ? `0${n}` : n) : '00';
};

// 判断字符串字节长度
export const getByteLen = (val) => {
    let len = 0;
    for (let i = 0; i < val.length; i++) {
        const a = val.charAt(i);
        // eslint-disable-next-line no-control-regex
        if (a.match(/[^\x00-\xff]/gi) != null) {
            len += 2;
        } else {
            len += 1;
        }
    }
    return len;
};

/**
 * @name sliceByte 按字节切割字符串
 * @param {string} str 要切割的字符串
 * @param {number} len 切割长度
 */
export const sliceByte = (str, len) => {
    if (!str && typeof str !== 'undefined') {
        return '';
    }
    let num = 0;
    const str1 = str;
    let res = '';
    for (let i = 0, lens = str1.length; i < lens; i++) {
        num += str1.charCodeAt(i) > 255 ? 2 : 1;
        if (num > len) {
            break;
        } else {
            res = str1.substring(0, i + 1);
        }
    }
    return res;
};

/**
 * @name showToast toast
 * @param {string} text 内容
 * @param {number} time 时间
 */
export const showToast = (text, time = 2000) => {
    window.f7.toast.show({
        text: text,
        closeTimeout: time,
        position: 'center',
    });
};


// 子元素滚动到父元素中间
export function scrollIntoViewMiddle(childElem, parentElem) {
    const parentRect = parentElem.getBoundingClientRect();
    const childRect = childElem.getBoundingClientRect();
    const parentWidth = parentElem.clientWidth;
    const parentHeight = parentElem.clientHeight;
    const childWidth = childElem.offsetWidth;
    const childHeight = childElem.offsetHeight;
    const parentTop = parentRect.top;
    const parentLeft = parentRect.left;
    const childTop = childRect.top;
    const childLeft = childRect.left;
    const scrollYDistance = childTop - parentTop + childHeight / 2 - parentHeight / 2
    const scrollXDistance = childLeft - parentLeft + childWidth / 2 - parentWidth / 2
    parentElem.scrollTo({
        left: parentElem.scrollLeft + scrollXDistance,
        top: parentElem.scrollTop + scrollYDistance,
        behavior: 'smooth'
    });
}