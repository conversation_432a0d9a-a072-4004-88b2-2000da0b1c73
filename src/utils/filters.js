import Vue from 'vue';
import { formatTime, omitTxt, getAvatar, omitValue, num2percent, supplementDouble } from '../utils/utils';

/**
 * @name formatTime 格式化时间
 * @example {{ value | formatTime('yyyy-MM-dd')}}
 */
Vue.filter('formatTime', formatTime);

/**
 * @name formatTxt 字数省略
 * @example {{ value | formatTxt(5)}}
 */
Vue.filter('formatTxt', omitTxt);

/**
 * @name head 获取头像
 * @example {{ account | head(1)}}
 */
Vue.filter('head', getAvatar);

/**
 * @name formatW 省略数值
 * @example {{ value | formatW(100000, 'w')}}
 */
Vue.filter('formatW', omitValue);

/**
 * @name num2percent 数字转百分比
 * @example {{ value | num2percent }}
 */
Vue.filter('num2percent', num2percent);

/**
 * @name supplementDouble 补齐数值两位
 * @example {{ value | supplementDouble }}
 */
Vue.filter('supplementDouble', supplementDouble);
