// src/stores/modules/useLiveTalentRankStore.js
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { liveTalentRequest } from '@/store/actions';

export const useLiveTalentRankStore = defineStore('liveTalentRank', () => {
    // 状态定义 - 赛区 1:音乐 2:情感 3:二次元 4:脱口秀
    const liveTalentRank = ref({
        1: [], // 音乐
        2: [], // 情感
        3: [], // 二次元
        4: [], // 脱口秀
    });

    // Getters
    const getMusicTalentRank = () => liveTalentRank.value[1] || [];
    const getEmotionTalentRank = () => liveTalentRank.value[2] || [];
    const getAnimeTalentRank = () => liveTalentRank.value[3] || [];
    const getTalkShowTalentRank = () => liveTalentRank.value[4] || [];

    // Actions
    async function updateLiveTalentRank(rankArea = 0) {
        try {
            const { code, data } = await liveTalentRequest({
                state: { request: { proPrefix: '/activity.Activity/' } },
                api: 'honorListGroup',
            });

            if (code === 0 && data?.group) {
                // 处理才艺排行榜数据
                data.group.forEach((item) => {
                    const { raceId } = item;
                    if (liveTalentRank.value.hasOwnProperty(raceId)) {
                        liveTalentRank.value[raceId] = item.list || [];
                    }
                });
            }

            return { code };
        } catch (error) {
            console.error('更新直播才艺排行榜失败:', error);
            return false;
        }
    }

    function setLiveTalentRank(raceId, data) {
        if (liveTalentRank.value.hasOwnProperty(raceId)) {
            liveTalentRank.value[raceId] = data;
        }
    }

    function resetLiveTalentRank() {
        liveTalentRank.value = {
            1: [],
            2: [],
            3: [],
            4: [],
        };
    }

    return {
        // 状态
        liveTalentRank,

        // Getters
        getMusicTalentRank,
        getEmotionTalentRank,
        getAnimeTalentRank,
        getTalkShowTalentRank,

        // Actions
        updateLiveTalentRank,
        setLiveTalentRank,
        resetLiveTalentRank,
    };
});
