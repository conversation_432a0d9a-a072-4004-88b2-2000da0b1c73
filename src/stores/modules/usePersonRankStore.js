// src/stores/modules/usePersonRankStore.js
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { yuleRequest } from '@/store/actions';

export const usePersonRankStore = defineStore('personRank', () => {
    // 状态定义
    const personRank = ref({
        mvp: [], // mvp
        star: [], // 踏浪榜
        fire: [], // 逐风榜
        wind: [], // 擎天榜
        sky: [], // 裂空榜
    });

    // Getters
    const getWindRank = () => personRank.value.wind || [];
    const getStarRank = () => personRank.value.star || [];
    const getFireRank = () => personRank.value.fire || [];
    const getSkyRank = () => personRank.value.sky || [];
    const getMvpRank = () => personRank.value.mvp || [];

    // Actions
    async function updatePersonalRank() {
        try {
            const { code, data } = await yuleRequest({
                state: { request: { proPrefix: '/activity.Activity/' } },
                api: 'gloryPersonalRanking',
                data: {
                    page: 1,
                    size: 10,
                    area: 'wave',
                },
            });

            if (code === 0 && data) {
                // 处理数据结构，使得data为{wave:[],wind:[],sky:[],void:[]}
                const keys = Object.keys(data);
                keys.forEach((type) => {
                    if (Object.prototype.hasOwnProperty.call(personRank.value, type)) {
                        personRank.value[type] = data[type]?.list || data[type] || [];
                    }
                });
            }

            return { code };
        } catch (error) {
            console.error('更新个人排行榜失败:', error);
            return false;
        }
    }

    function setPersonRank(key, data) {
        if (Object.prototype.hasOwnProperty.call(personRank.value, key)) {
            personRank.value[key] = data;
        }
    }

    function resetPersonRank() {
        personRank.value = {
            mvp: [],
            star: [],
            fire: [],
            wind: [],
            sky: [],
        };
    }

    return {
        // 状态
        personRank,

        // Getters
        getWindRank,
        getStarRank,
        getFireRank,
        getSkyRank,
        getMvpRank,

        // Actions
        updatePersonalRank,
        setPersonRank,
        resetPersonRank,
    };
});
