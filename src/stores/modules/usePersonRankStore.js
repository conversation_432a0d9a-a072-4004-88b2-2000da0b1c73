/* eslint-disable function-paren-newline */
// src/stores/modules/usePersonRankStore.js
// eslint-disable-next-line import/no-extraneous-dependencies
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { yuleRequest } from '@/store/actions';

const usePersonRankStore = defineStore('personRank', () => {
    // 状态定义
    const personRank = ref({
        mvp: [], // mvp
        wave: [], // 踏浪榜
        wind: [], // 逐风榜
        sky: [], // 擎天榜
        void: [], // 裂空榜
    });

    // Getters
    const getWindRank = () => personRank.value.wind || [];
    const getWaveRank = () => personRank.value.wave || [];
    const getVoidRank = () => personRank.value.void || [];
    const getSkyRank = () => personRank.value.sky || [];
    const getMvpRank = () => personRank.value.mvp || [];

    // Actions
    async function updatePersonalRank() {
        try {
            // 定义需要请求的区域
            const areas = ['wave', 'wind', 'sky', 'void'];

            // 并行请求所有区域的数据
            const requests = areas.map((area) =>
                yuleRequest({
                    api: 'gloryPersonalRanking',
                    data: {
                        page: 1,
                        size: 10,
                        area,
                    },
                }),
            );

            const results = await Promise.all(requests);

            // 处理每个区域的响应数据
            results.forEach((result, index) => {
                const area = areas[index];
                const { code, data } = result;

                if (code === 0 && data?.list) {
                    // 将每个区域的数据存储到对应的字段
                    personRank.value[area] = data.list;
                }
            });

            return { code: 0 };
        } catch (error) {
            console.error('更新个人排行榜失败:', error);
            return false;
        }
    }

    function setPersonRank(key, data) {
        if (Object.prototype.hasOwnProperty.call(personRank.value, key)) {
            personRank.value[key] = data;
        }
    }

    function resetPersonRank() {
        personRank.value = {
            mvp: [],
            wave: [],
            void: [],
            wind: [],
            sky: [],
        };
    }

    return {
        // 状态
        personRank,

        // Getters
        getWindRank,
        getWaveRank,
        getVoidRank,
        getSkyRank,
        getMvpRank,

        // Actions
        updatePersonalRank,
        setPersonRank,
        resetPersonRank,
    };
});

export { usePersonRankStore };
export default usePersonRankStore;
