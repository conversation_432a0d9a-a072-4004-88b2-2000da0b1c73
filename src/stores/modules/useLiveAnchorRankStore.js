// src/stores/modules/useLiveAnchorRankStore.js
// eslint-disable-next-line import/no-extraneous-dependencies
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { liveRequest } from '@/store/actions';

const useLiveAnchorRankStore = defineStore('liveAnchorRank', () => {
    // 状态定义
    const liveAnchorRank = ref({
        music: [], // 音乐
        emotion: [], // 情感
        newstar: [], // 新星
        talent: [], // 才艺
        mvp: [], // mvp
    });

    // Getters
    const getMusicRank = () => liveAnchorRank.value.music || [];
    const getEmotionRank = () => liveAnchorRank.value.emotion || [];
    const getNewstarRank = () => liveAnchorRank.value.newstar || [];
    const getTalentRank = () => liveAnchorRank.value.talent || [];
    const getMvpRank = () => liveAnchorRank.value.mvp || [];

    // Actions
    async function updateLiveAnchorRank() {
        try {
            const { code, data } = await liveRequest({
                state: { request: { proPrefix: '/honor.Honor/' } },
                api: 'anchorHonor',
            });

            if (code === 0 && data) {
                // 更新各个赛道的数据
                Object.keys(data).forEach((key) => {
                    if (Object.prototype.hasOwnProperty.call(liveAnchorRank.value, key)) {
                        liveAnchorRank.value[key] = data[key] || [];
                    }
                });
            }

            return { code };
        } catch (error) {
            console.error('更新直播主播排行榜失败:', error);
            return false;
        }
    }

    function setLiveAnchorRank(key, data) {
        if (Object.prototype.hasOwnProperty.call(liveAnchorRank.value, key)) {
            liveAnchorRank.value[key] = data;
        }
    }

    function resetLiveAnchorRank() {
        liveAnchorRank.value = {
            music: [],
            emotion: [],
            newstar: [],
            talent: [],
            mvp: [],
        };
    }

    return {
        // 状态
        liveAnchorRank,

        // Getters
        getMusicRank,
        getEmotionRank,
        getNewstarRank,
        getTalentRank,
        getMvpRank,

        // Actions
        updateLiveAnchorRank,
        setLiveAnchorRank,
        resetLiveAnchorRank,
    };
});

export { useLiveAnchorRankStore };
export default useLiveAnchorRankStore;
