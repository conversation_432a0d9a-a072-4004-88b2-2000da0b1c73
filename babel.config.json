{"presets": [["@babel/preset-env", {"loose": true, "modules": false, "useBuiltIns": "usage", "corejs": 3, "debug": true, "targets": ["> 1%", "last 2 versions", "not ie <= 10", "Android >= 5.0", "iOS >= 8"]}]], "plugins": ["@babel/plugin-transform-runtime", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-optional-chaining"], "sourceType": "unambiguous"}