// 须知：
// 1、全局安装 protobufjs@6.10.2版本 npm包 - npm install protobufjs@6.10.2 -g
// 2、全局安装 prettier npm包 - npm install prettier -g
// 3、在项目根目录中执行 node ./pb2api $backendId $protoFileName
// backendId 默认为config.nodePath
// protoFileName 默认为 activity
// eg node ./pb.js puzzle-sign-2025
const { execSync } = require('child_process');
const assert = require('assert');
const fs = require('fs');
const path = require('path');

const config = fs.readFileSync('./src/config.js', 'utf-8');
const backendId = process.argv[2] || (config.match(/nodePath:\s*'([^']+)'/) || [])[1];
const protoFileName = process.argv[3] || 'activity';
assert(backendId, '请传入后端活动ID');

function rmFile(file) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) fs.rmSync(filePath, { recursive: true, force: true });
}

async function run() {
    rmFile(backendId);
    execSync(`<NAME_EMAIL>:frontend/nodejs/activity/${backendId}.git`);
    assert(fs.existsSync(`./${backendId}`), '后端项目不存在');
    fs.copyFileSync(`./${backendId}/typings/controllers/${protoFileName}.d.ts`, 'src/store/api.d.ts');
    execSync(`pbjs -t json ./${backendId}/protos/service/${protoFileName}.proto -o  activity.json`);
    require('./pbmap2js')();
    execSync('prettier --write src/store/apis.js src/store/mockData.js');
    rmFile(backendId);
    rmFile('activity.json');
}

run().catch(console.error);
