// https://eslint.org/docs/user-guide/configuring
const { join } = require('path');

module.exports = {
    root: true,
    parser: 'vue-eslint-parser',
    parserOptions: {
        sourceType: 'module',
        parser: '@babel/eslint-parser',
        ecmaVersion: 6,
    },
    env: {
        browser: true,
        node: true,
        jest: true,
    },
    // exclude: ['.eslintrc.js'],
    // https://github.com/vuejs/eslint-plugin-vue#priority-a-essential-error-prevention
    extends: ['airbnb-base', 'plugin:vue/recommended'],
    // required to lint *.vue files
    plugins: ['vue', 'promise'],
    settings: {
        'import/resolver': {
            webpack: {
                config: join(__dirname, './build/webpack.common.js'),
            },
        },
        'import/extensions': ['.js', '.vue', 'json'],
    },
    // add your custom rules here
    rules: {
        // don't require .vue extension when importing 导入无需后缀的文件
        'import/extensions': [
            'error',
            'always',
            {
                js: 'never',
                vue: 'never',
            },
        ],
        // disallow reassignment of function parameters
        // disallow parameter object manipulation except for specific exclusions
        // 函数传参修改判定
        'no-param-reassign': [
            'error',
            {
                props: true,
                ignorePropertyModificationsFor: [
                    'state', // for vuex state
                    'acc', // for reduce accumulators
                    'e', // for e.returnvalue
                ],
            },
        ],
        'no-console': 'warn',
        // allow existedDependencies
        'import/no-extraneous-dependencies': [
            'error',
            {
                optionalDependencies: ['test/unit/index.js'],
            },
        ],
        // 前端项目忽略commonjs
        'import/no-unresolved': [2, { commonjs: false, amd: true }],
        'import/no-absolute-path': [2, { commonjs: false, amd: true }],
        // 导入依赖后空一行
        'import/newline-after-import': ['warn', { count: 1 }],
        // allow paren-less arrow functions
        // 'arrow-parens': 0,
        // allow debugger during development
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        // 强制单引号
        quotes: ['error', 'single'],
        // indentation off
        indent: [
            'error',
            4,
            {
                SwitchCase: 1,
            },
        ],
        curly: 'off',
        // 禁止使用var
        'no-var': 'error',
        // allow no unused vars
        'no-unused-vars': ['warn', { vars: 'all', args: 'none', ignoreRestSiblings: false }],
        // allow exceeds the maximum line length of 80
        // 'max-len': ['error', { code: 80 }],
        'max-len': 'off',
        // 不强迫单行的运算一定要加空行 TODO
        'nonblock-statement-body-position': [2, 'any'],
        // 格式化后，超长换行的运算符位置不确定，因此禁了..
        'operator-linebreak': 0,
        // 在for循环里才允许++
        'no-plusplus': ['error', { allowForLoopAfterthoughts: true }],
        // 警告跟外部的变量重名以及全局
        'no-shadow': [1, { builtinGlobals: true }],
        // 对象换行规范，太复杂关闭
        'object-curly-newline': 'off',
        'global-require': 'off',
        'implicit-arrow-linebreak': 'off',
        // 'no-mixed-spaces-and-tabs': 'off',
        // "quotes":"off",
        // 要沟通是否开启组件kebab-case命名
        // 'vue/multi-word-component-names': ['error', { ignore: [] }],
        'vue/multi-word-component-names': 'off',
        // 'vue/component-definition-name-casing': ['error', 'kebab-case'],
        // 组件命名规范，驼峰或者横杆 TODO
        'vue/component-definition-name-casing': ['error', 'kebab-case'],
        // 标签单独闭合
        'vue/html-self-closing': [
            'off',
            {
                html: {
                    void: 'any',
                    normal: 'any',
                    component: 'any',
                },
                svg: 'always',
                math: 'always',
            },
        ],
        'vue/html-closing-bracket-newline': 'off',
        // 强制要求文本前换行，关闭，太麻烦
        'vue/multiline-html-element-content-newline': 'off',
        // 属性排序，没必要
        'vue/attributes-order': 'off',
        // 属性换行操作，跟vetur排序冲突，禁用
        'vue/first-attribute-linebreak': 'off',
        // 单行元素内容是否需要单独一行
        'vue/singleline-html-element-content-newline': 'off',
        // 变量的前后空格，禁用
        'vue/mustache-interpolation-spacing': 'off',
        // 跟格式化风格冲突了
        'vue/html-indent': 0,
        // 禁止使用v-html来防止xss攻击
        'vue/no-v-html': 'off',
        //
        // 每个属性单独一行
        'vue/max-attributes-per-line': [
            'error',
            {
                singleline: {
                    max: 1,
                },
                multiline: {
                    max: 1,
                },
            },
        ],
    },
    globals: {
        f7: true,
        f7modal: true,
        f7toast: true,
        f7preloader: true,
        TTJSBridge: true,
        myWebview: true,
        VConsole: true,
        API_URL: true,
        NODE_URL: true,
        WEB_URL: true,
        AVATAR_URL: true,
        JS_BRIDGE_PROTOCOL: true,
        JS_BRIDGE_DOMAIN: true,
        WEBSITE: true,
        RELEASE: true,
    },
};
