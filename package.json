{"name": "anniversary-guild-honor-2025", "version": "1.0.0", "description": "webpack5 template for mf", "main": "index.js", "private": true, "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "cross-env NODE_ENV=dev webpack serve --config build/webpack.dev.js", "build": "cross-env webpack --config build/webpack.prod.js", "offline": "node build/manage-offline/index.js", "lint-staged": "lint-staged", "lint": "eslint --ext .js,.vue src", "prepare": "if [ -n \"$CI_PROJECT_PATH_SLUG\" ]; then echo skip husky install; else chmod 777 .husky/* && .husky/git-check.sh; fi"}, "author": "crai<PERSON><PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "^7.21.4", "@babel/eslint-parser": "^7.21.3", "@babel/parser": "^7.21.4", "@babel/plugin-proposal-export-namespace-from": "^7.16.7", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-runtime": "^7.21.4", "@babel/preset-env": "^7.21.4", "@babel/runtime": "^7.17.2", "@commitlint/cli": "^17.6.5", "@commitlint/config-conventional": "^17.6.5", "@soda/friendly-errors-webpack-plugin": "^1.8.1", "address": "^1.1.2", "autoprefixer": "^10.4.0", "chalk": "^4.1.2", "core-js": "^3.30.0", "cross-env": "^7.0.3", "css-loader": "^6.5.1", "esbuild-loader": "^2.21.0", "eslint": "^8.38.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-standard": "^17.0.0", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-import": "2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "6.1.1", "eslint-plugin-vue": "^9.10.0", "eslint-webpack-plugin": "^4.0.1", "html-loader": "^4.2.0", "html-webpack-plugin": "^5.5.0", "husky": "^8.0.3", "less": "^4.1.2", "less-loader": "^11.1.0", "lint-staged": "^13.2.2", "mini-css-extract-plugin": "^2.7.5", "node-notifier": "^10.0.1", "portfinder": "^1.0.28", "postcss": "^8.4.4", "postcss-loader": "^7.2.4", "postcss-preset-env": "^8.3.1", "postcss-px-to-viewport": "^1.1.1", "serialize-javascript": "^6.0.1", "style-resources-loader": "^1.5.0", "tinify": "^1.6.0-beta.2", "unplugin-vue-components": "^0.24.1", "vue-loader": "^15.9.8", "vue-style-loader": "^4.1.3", "vue-template-compiler": "^2.6.14", "webpack": "^5.78.0", "webpack-bundle-analyzer": "^4.5.0", "webpack-cli": "^5.0.2", "webpack-dev-server": "^4.13.2"}, "dependencies": {"@tt/active-app": "^1.4.4", "@tt/bylink-sdk": "^2.0.7", "@tt/tween": "^1.0.1", "axios": "1.3.5", "dayjs": "^1.11.7", "framework7": "^5.7.14", "framework7-vue": "^5.7.14", "tailwindcss": "^3.3.1", "vue": "^2.6.14", "vuex": "^3.6.2"}, "lint-staged": {"*.{js,vue}": "eslint --cache --fix", "*.{css,md}": "prettier --write", "package.json": "prettier --write"}}