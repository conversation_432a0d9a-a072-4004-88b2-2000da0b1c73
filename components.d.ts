/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ActivityEndModal: typeof import('./src/components/common/activity-end-modal.vue')['default']
    AnimeTalent: typeof import('./src/components/person/AnimeTalent.vue')['default']
    ArrowBtn: typeof import('./src/components/common/arrow-btn.vue')['default']
    BlackGoldPerson: typeof import('./src/components/person/BlackGoldPerson.vue')['default']
    Cp: typeof import('./src/components/other/Cp.vue')['default']
    CurrentServerTime: typeof import('./src/components/common/current-server-time.vue')['default']
    DiamondGuild: typeof import('./src/components/guild/DiamondGuild.vue')['default']
    EmotionPerson: typeof import('./src/components/person/EmotionPerson.vue')['default']
    EmotionTalent: typeof import('./src/components/person/EmotionTalent.vue')['default']
    FirePerson: typeof import('./src/components/person/FirePerson.vue')['default']
    FullPage: typeof import('./src/components/common/full-page.vue')['default']
    Guild: typeof import('./src/components/guild/Guild.vue')['default']
    GuildList: typeof import('./src/components/common/guild-list.vue')['default']
    HonorGuild: typeof import('./src/components/guild/HonorGuild.vue')['default']
    Hot: typeof import('./src/components/other/Hot.vue')['default']
    HotOther: typeof import('./src/components/other/HotOther.vue')['default']
    Hybrid: typeof import('./src/components/common/beluga/hybrid.vue')['default']
    KingGuild: typeof import('./src/components/guild/KingGuild.vue')['default']
    Knight: typeof import('./src/components/other/Knight.vue')['default']
    LiveHot: typeof import('./src/components/other/LiveHot.vue')['default']
    LiveKingGuild: typeof import('./src/components/guild/LiveKingGuild.vue')['default']
    LiveMvp: typeof import('./src/components/person/LiveMvp.vue')['default']
    LivePerson: typeof import('./src/components/person/LivePerson.vue')['default']
    LiveRoomStatus: typeof import('./src/components/common/live-room-status.vue')['default']
    LiveTalent: typeof import('./src/components/person/LiveTalent.vue')['default']
    MusicPerson: typeof import('./src/components/person/MusicPerson.vue')['default']
    MusicTalent: typeof import('./src/components/person/MusicTalent.vue')['default']
    NavBtn: typeof import('./src/components/common/nav-btn.vue')['default']
    NewStarPerson: typeof import('./src/components/person/NewStarPerson.vue')['default']
    Open: typeof import('./src/components/other/Open.vue')['default']
    PageNavBar: typeof import('./src/components/common/page-nav-bar.vue')['default']
    PagePtrPreloader: typeof import('./src/components/common/page-ptr-preloader.vue')['default']
    PageTopBar: typeof import('./src/components/common/page-top-bar.vue')['default']
    QiangTing: typeof import('./src/components/other/QiangTing.vue')['default']
    QiangTing2D: typeof import('./src/components/other/QiangTing2D.vue')['default']
    QiangTingFinal: typeof import('./src/components/other/QiangTingFinal.vue')['default']
    QiangTingGame: typeof import('./src/components/other/QiangTingGame.vue')['default']
    QiangTingInteraction: typeof import('./src/components/other/QiangTingInteraction.vue')['default']
    QiangTingMusic: typeof import('./src/components/other/QiangTingMusic.vue')['default']
    RankDetailsContent: typeof import('./src/components/common/rank-details-content.vue')['default']
    RankRightTab: typeof import('./src/components/common/rank-right-tab.vue')['default']
    RankTitle: typeof import('./src/components/common/rank-title.vue')['default']
    RecreationMvp: typeof import('./src/components/person/RecreationMvp.vue')['default']
    RecreationOther: typeof import('./src/components/other/RecreationOther.vue')['default']
    RecreationPerson: typeof import('./src/components/person/RecreationPerson.vue')['default']
    Rich: typeof import('./src/components/other/Rich.vue')['default']
    RoomDetailsContent: typeof import('./src/components/common/room-details-content.vue')['default']
    RoomStatus: typeof import('./src/components/common/room-status.vue')['default']
    ShareModal: typeof import('./src/components/common/share-modal.vue')['default']
    ShareSheet: typeof import('./src/components/common/share-sheet.vue')['default']
    SkyPerson: typeof import('./src/components/person/SkyPerson.vue')['default']
    StarPerson: typeof import('./src/components/person/StarPerson.vue')['default']
    StatusBar: typeof import('./src/components/common/status-bar.vue')['default']
    TalentPerson: typeof import('./src/components/person/TalentPerson.vue')['default']
    TalkTalent: typeof import('./src/components/person/TalkTalent.vue')['default']
    WindPerson: typeof import('./src/components/person/WindPerson.vue')['default']
  }
}
