[{"/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/pb.js": "1", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/components/common/share-modal.vue": "2", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/components/common/share-sheet.vue": "3", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/config.js": "4", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/links.js": "5", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/pages/Home.vue": "6", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/routes.js": "7", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/store/actions.js": "8", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/store/getters.js": "9", "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/store/mock.js": "10"}, {"size": 1493, "mtime": 1751963666991, "results": "11", "hashOfConfig": "12"}, {"size": 25707, "mtime": 1751963666992}, {"size": 8140, "mtime": 1751965211263}, {"size": 2293, "mtime": 1751963666992, "results": "13", "hashOfConfig": "12"}, {"size": 568, "mtime": 1751963666992, "results": "14", "hashOfConfig": "12"}, {"size": 10180, "mtime": 1751965161725, "results": "15", "hashOfConfig": "12"}, {"size": 186, "mtime": 1751964572848, "results": "16", "hashOfConfig": "12"}, {"size": 10047, "mtime": 1751964562833, "results": "17", "hashOfConfig": "12"}, {"size": 420, "mtime": 1751963666993, "results": "18", "hashOfConfig": "12"}, {"size": 2174, "mtime": 1751963666993, "results": "19", "hashOfConfig": "12"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1as9mow", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/pb.js", ["44"], [], "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/config.js", [], [], "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/links.js", ["45", "46"], [], "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/pages/Home.vue", ["47", "48"], ["49", "50"], "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/routes.js", [], [], "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/store/actions.js", ["51", "52", "53"], [], "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/store/getters.js", [], [], "/Users/<USER>/Documents/2025/anniversary-guild-honor-2024/src/store/mock.js", [], ["54"], {"ruleId": "55", "severity": 1, "message": "56", "line": 35, "column": 13, "nodeType": "57", "messageId": "58", "endLine": 35, "endColumn": 26}, {"ruleId": "59", "severity": 1, "message": "60", "line": 2, "column": 8, "nodeType": "61", "messageId": "62", "endLine": 2, "endColumn": 17}, {"ruleId": "55", "severity": 1, "message": "56", "line": 9, "column": 1, "nodeType": "57", "messageId": "58", "endLine": 9, "endColumn": 12, "suggestions": "63"}, {"ruleId": "59", "severity": 1, "message": "64", "line": 170, "column": 31, "nodeType": "61", "messageId": "62", "endLine": 170, "endColumn": 37}, {"ruleId": "55", "severity": 1, "message": "56", "line": 225, "column": 13, "nodeType": "57", "messageId": "58", "endLine": 225, "endColumn": 24, "suggestions": "65"}, {"ruleId": "66", "severity": 2, "message": "67", "line": 145, "column": 48, "nodeType": "68", "messageId": "69", "endLine": 145, "endColumn": 56, "suppressions": "70"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 200, "column": 29, "nodeType": "57", "messageId": "58", "endLine": 200, "endColumn": 40, "suggestions": "71", "suppressions": "72"}, {"ruleId": "73", "severity": 1, "message": "74", "line": 15, "column": 7, "nodeType": "61", "messageId": "75", "endLine": 15, "endColumn": 14}, {"ruleId": "73", "severity": 1, "message": "76", "line": 179, "column": 13, "nodeType": "61", "messageId": "75", "endLine": 179, "endColumn": 17}, {"ruleId": "59", "severity": 1, "message": "77", "line": 179, "column": 13, "nodeType": "61", "messageId": "78", "endLine": 179, "endColumn": 17}, {"ruleId": "55", "severity": 1, "message": "56", "line": 55, "column": 5, "nodeType": "57", "messageId": "58", "endLine": 55, "endColumn": 17, "suggestions": "79", "suppressions": "80"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", "no-shadow", "'myWebview' is already a global variable.", "Identifier", "noShadowGlobal", ["81"], "'origin' is already a global variable.", ["82"], "new-cap", "A constructor name should not start with a lowercase letter.", "NewExpression", "lower", ["83"], ["84"], ["85"], "no-unused-vars", "'request' is assigned a value but never used.", "unusedVar", "'init' is assigned a value but never used.", "'init' is already declared in the upper scope on line 178 column 14.", "noShadow", ["86"], ["87"], {"messageId": "88", "data": "89", "fix": "90", "desc": "91"}, {"messageId": "88", "data": "92", "fix": "93", "desc": "91"}, {"kind": "94", "justification": "95"}, {"messageId": "88", "data": "96", "fix": "97", "desc": "91"}, {"kind": "94", "justification": "95"}, {"messageId": "88", "data": "98", "fix": "99", "desc": "100"}, {"kind": "94", "justification": "95"}, "removeConsole", {"propertyName": "101"}, {"range": "102", "text": "95"}, "Remove the console.log().", {"propertyName": "101"}, {"range": "103", "text": "95"}, "directive", "", {"propertyName": "101"}, {"range": "104", "text": "95"}, {"propertyName": "105"}, {"range": "106", "text": "95"}, "Remove the console.warn().", "log", [226, 251], [9337, 9369], [8251, 8275], "warn", [1680, 1771]]